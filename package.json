{"name": "ame-star-typesetting-editor", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@excalidraw/excalidraw": "^0.18.0", "@lexical/clipboard": "0.35.0", "@lexical/code": "0.35.0", "@lexical/code-shiki": "0.35.0", "@lexical/file": "0.35.0", "@lexical/hashtag": "0.35.0", "@lexical/html": "^0.35.0", "@lexical/link": "0.35.0", "@lexical/list": "0.35.0", "@lexical/mark": "0.35.0", "@lexical/overflow": "0.35.0", "@lexical/plain-text": "0.35.0", "@lexical/react": "0.35.0", "@lexical/rich-text": "0.35.0", "@lexical/selection": "0.35.0", "@lexical/table": "0.35.0", "@lexical/utils": "0.35.0", "@reduxjs/toolkit": "^2.9.0", "axios": "^1.6.2", "date-fns": "^4.1.0", "katex": "^0.16.22", "lexical": "0.35.0", "lodash-es": "^4.17.21", "mammoth": "^1.10.0", "prettier": "^3.6.2", "react": "^18.2.0", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^3.1.4", "react-redux": "^9.0.4", "y-websocket": "^1.5.4", "yjs": ">=13.5.42"}, "devDependencies": {"@types/katex": "^0.16.7", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}}