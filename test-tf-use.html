<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>医学论文段落分类</title>
  <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest/dist/tf.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/universal-sentence-encoder@1.2.1/dist/universal-sentence-encoder.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      background-color: #f3f4f6;
    }
    .container {
      max-width: 800px;
    }
    .rounded-box {
      border-radius: 1rem;
    }
    /* Simple transition for smooth enabling/disabling */
    .disabled-section {
        opacity: 0.5;
        pointer-events: none;
    }
  </style>
</head>

<body class="bg-gray-100 flex items-center justify-center min-h-screen p-4">

  <div class="container bg-white p-8 rounded-box shadow-lg w-full">
    <h1 class="text-3xl font-bold mb-6 text-center text-gray-800">医学论文段落分类</h1>
    <p class="mb-6 text-center text-gray-600">
      此应用使用 Universal Sentence Encoder (USE) 来预测医学论文段落的类型。您可以选择使用默认数据进行训练，或上传您自己的 JSON 文件。
    </p>
    
    <!-- Step 1: Training Options -->
    <div id="trainingSection" class="mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
        <h2 class="text-2xl font-bold mb-4 text-center text-gray-700">第一步: 训练模型</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 items-start">
            <!-- Option A: Default Data -->
            <div class="flex flex-col items-center">
                <p class="text-gray-600 mb-3 text-center">使用内置的示例数据集快速开始。</p>
                <button id="trainDefaultButton" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-4 rounded-md transition duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                    使用默认数据训练
                </button>
            </div>
            <!-- Option B: Upload Data -->
            <div class="flex flex-col items-center">
                 <label for="fileInput" class="block text-gray-700 font-semibold mb-2">或上传您的训练数据:</label>
                 <input type="file" id="fileInput" accept=".json" class="w-full text-sm text-gray-500
                    file:mr-4 file:py-2 file:px-4
                    file:rounded-full file:border-0
                    file:text-sm file:font-semibold
                    file:bg-blue-50 file:text-blue-700
                    hover:file:bg-blue-100
                  "/>
                <p class="text-xs text-gray-500 mt-2 text-center">
                    请上传 JSON 文件，格式为: <br> `[{"text": "...", "label": "..."}, ...]`
                </p>
                <button id="trainUploadedButton" disabled class="mt-3 w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-md transition duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed">
                    使用上传文件训练
                </button>
            </div>
        </div>
    </div>
    
    <!-- Step 2: Classification -->
    <div id="classificationSection" class="mb-4 disabled-section">
        <h2 class="text-2xl font-bold mb-4 text-center text-gray-700">第二步: 分类新段落</h2>
      <label for="paragraphInput" class="block text-gray-700 font-semibold mb-2">输入段落:</label>
      <textarea id="paragraphInput" rows="4" class="w-full p-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200"
        placeholder="模型训练完成后，在此输入一个医学论文段落..."></textarea>
      <button id="classifyButton" class="mt-4 w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-md transition duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
        <span id="buttonText">分类</span>
        <div id="spinner" class="hidden animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mx-auto"></div>
      </button>
    </div>

    <!-- Step 3: Results -->
    <div class="mt-8 bg-gray-50 p-6 rounded-md shadow-inner">
      <h2 class="text-xl font-bold mb-4 text-gray-700">模型输出</h2>
      <div id="status" class="text-gray-600 mb-4">请先选择一种方式训练模型。</div>
      <div id="progressBarContainer" class="w-full bg-gray-200 rounded-full h-2.5 hidden mb-4">
        <div id="progressBar" class="bg-blue-600 h-2.5 rounded-full transition-all duration-300" style="width: 0%"></div>
      </div>
      <div id="results" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"></div>
    </div>
  </div>

  <script>
    // DOM Elements
    const trainDefaultButton = document.getElementById('trainDefaultButton');
    const fileInput = document.getElementById('fileInput');
    const trainUploadedButton = document.getElementById('trainUploadedButton');
    const classificationSection = document.getElementById('classificationSection');
    const paragraphInput = document.getElementById('paragraphInput');
    const classifyButton = document.getElementById('classifyButton');
    const buttonText = document.getElementById('buttonText');
    const spinner = document.getElementById('spinner');
    const statusDiv = document.getElementById('status');
    const resultsDiv = document.getElementById('results');
    const progressBarContainer = document.getElementById('progressBarContainer');
    const progressBar = document.getElementById('progressBar');

    // Default training data
    const defaultMedicalData = [
      { text: "A Novel Method for Diagnosing Diabetes Mellitus Based on Deep Learning", label: "title" },
      { text: "John Smith, MD, Jane Doe, PhD", label: "author" },
      { text: "1. Department of Internal Medicine, University of California, San Francisco", label: "address" },
      { text: "2. Department of Biomedical Engineering, Stanford University", label: "address" },
      { text: "Background: Diabetes mellitus is a major public health concern...", label: "abstract" },
      { text: "Methods: We collected a dataset of 1000 patients...", label: "body" },
      { text: "Results: The proposed model achieved an accuracy of 98.5%...", label: "body" },
      { text: "Figure 1. The architecture of the proposed neural network.", label: "figure" },
      { text: "[1] Smith J, Doe J. A new approach to diabetes diagnosis. Journal of Medical Science. 2023;45(2):123-130.", label: "references" },
      { text: "Clinical Significance: This study provides a significant contribution to early diabetes detection.", label: "abstract" },
      { text: "Introduction: Deep learning has shown remarkable success in various medical applications...", label: "body" },
      { text: "Table 1. Performance comparison of different models.", label: "figure" },
      { text: "Conclusion: In summary, this study validates the efficacy of the proposed method in a clinical setting.", label: "abstract" },
      { text: "A Comprehensive Review of Cancer Treatment Methodologies", label: "title" },
      { text: "Dr. Alice Johnson, Dr. Bob Williams", label: "author" },
      { text: "Department of Oncology, New York Medical Center", label: "address" },
      { text: "The primary aim of this study was to evaluate the effectiveness of chemotherapy.", label: "abstract" },
      { text: "Materials and Methods: Tumor samples were collected from 50 patients.", label: "body" },
      { text: "Results showed a significant reduction in tumor size in the treatment group.", label: "body" },
      { text: "Figure 2. Survival rate of patients over a 5-year period.", label: "figure" },
      { text: "[2] Johnson A, Williams B. Advances in Oncology. Cancer Research Journal. 2022;15(1):50-60.", label: "references" },
      { text: "Acknowledgements: We thank the National Institutes of Health for their generous funding.", label: "references" },
    ];

    let customMedicalData = null;
    let labels = [];
    let labelToIndex = {};
    let numClasses = 0;
    
    let useModel;
    let classifier;

    // --- File Handling ---
    fileInput.addEventListener('change', (event) => {
        const file = event.target.files[0];
        if (!file) {
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                // Basic validation, now including a check for emptiness
                if (Array.isArray(data) && data.length > 0 && data.every(item => 'text' in item && 'label' in item)) {
                    customMedicalData = data;
                    trainUploadedButton.disabled = false;
                    statusDiv.textContent = `已成功加载 ${data.length} 条记录。点击按钮开始训练。`;
                } else if (!Array.isArray(data) || !data.every(item => 'text' in item && 'label' in item)) {
                    throw new Error("JSON 格式无效。应为包含 'text' 和 'label' 键的对​​象数组。");
                } else { // Handle empty array case
                    throw new Error("JSON 文件为空，请提供至少一条训练记录。");
                }
            } catch (error) {
                statusDiv.textContent = `加载文件失败: ${error.message}`;
                console.error(error);
                trainUploadedButton.disabled = true;
                customMedicalData = null;
            }
        };
        reader.readAsText(file);
    });
    
    // --- Model Preparation and Training ---
    async function initializeAndTrain(trainingData) {
        // Add a guard clause to prevent training on empty data
        if (!trainingData || trainingData.length === 0) {
            statusDiv.textContent = "错误：训练数据为空，无法开始训练。";
            enableTrainingButtons();
            return;
        }

        // Reset UI for new training session
        resultsDiv.innerHTML = '';
        progressBarContainer.classList.add('hidden');
        disableTrainingButtons();
        statusDiv.textContent = "正在加载 Universal Sentence Encoder (USE) 模型...";
        
        try {
            // Load USE model (if not already loaded)
            if (!useModel) {
                useModel = await use.load();
            }
            statusDiv.textContent = "USE 模型加载完成。正在处理数据...";

            // Determine labels from data
            const uniqueLabels = [...new Set(trainingData.map(d => d.label))];
            labels = uniqueLabels.sort();
            labelToIndex = Object.fromEntries(labels.map((label, i) => [label, i]));
            numClasses = labels.length;

            if (numClasses < 2) {
                throw new Error("训练数据必须至少包含两个不同的标签。");
            }
            
            // Prepare data: convert text to embeddings and labels to one-hot encoding
            const sentences = trainingData.map(d => d.text);
            const embeddings = await useModel.embed(sentences);
            const xs = embeddings;
            const ys = tf.tensor2d(trainingData.map(d => encodeLabel(d.label)), [trainingData.length, numClasses]);

            statusDiv.textContent = "数据准备完成。正在训练分类器...";
            
            // Build and train the classifier
            await trainClassifier(xs, ys);

        } catch (error) {
            statusDiv.textContent = `发生错误: ${error.message}`;
            console.error(error);
            progressBarContainer.classList.add('hidden'); // Hide progress bar on error
            enableTrainingButtons();
        }
    }

    function encodeLabel(label) {
      const encoded = new Array(numClasses).fill(0);
      const index = labelToIndex[label];
      if (index !== undefined) {
        encoded[index] = 1;
      }
      return encoded;
    }

    async function trainClassifier(xs, ys) {
      const embeddingSize = 512; // USE model's output dimension
      const totalEpochs = 50;
      classifier = tf.sequential();
      classifier.add(tf.layers.dense({ units: 128, activation: 'relu', inputShape: [embeddingSize] }));
      classifier.add(tf.layers.dropout({ rate: 0.5 }));
      classifier.add(tf.layers.dense({ units: numClasses, activation: 'softmax' }));

      classifier.compile({
        optimizer: tf.train.adam(0.001),
        loss: 'categoricalCrossentropy',
        metrics: ['accuracy']
      });

      await classifier.fit(xs, ys, {
          epochs: totalEpochs,
          batchSize: 16,
          shuffle: true,
          callbacks: {
            onTrainBegin: () => {
                progressBarContainer.classList.remove('hidden');
                progressBar.style.width = '0%';
            },
            onEpochEnd: (epoch, logs) => {
              console.log(`Epoch ${epoch + 1}: loss = ${logs.loss.toFixed(4)}, accuracy = ${logs.acc.toFixed(4)}`);
               const progress = ((epoch + 1) / totalEpochs) * 100;
               progressBar.style.width = `${progress}%`;
               statusDiv.textContent = `训练中... Epoch ${epoch + 1}/${totalEpochs}, 准确率: ${(logs.acc * 100).toFixed(2)}%`;
            }
          }
      });
      statusDiv.textContent = "分类器训练完成！您可以输入段落进行分类。";
      classificationSection.classList.remove('disabled-section');
    }

    // --- Classification ---
    async function classifyParagraph() {
      const paragraph = paragraphInput.value.trim();
      if (!paragraph) {
        statusDiv.textContent = "请输入一个段落进行分类！";
        return;
      }

      statusDiv.textContent = "正在进行分类...";
      resultsDiv.innerHTML = '';
      toggleLoading(true, "Classification");

      try {
        const embedding = await useModel.embed([paragraph]);
        const prediction = classifier.predict(embedding);
        const predictionArray = await prediction.data();
        
        displayResults(predictionArray);

      } catch (error) {
        statusDiv.textContent = "分类时出错，请检查控制台。";
        console.error(error);
      } finally {
        toggleLoading(false, "Classification");
      }
    }

    // --- UI Helper Functions ---
    function toggleLoading(isLoading, mode) {
        const btn = mode === "Classification" ? classifyButton : null;
        if(btn) {
            if (isLoading) {
                btn.disabled = true;
                btn.querySelector('#buttonText').classList.add('hidden');
                btn.querySelector('#spinner').classList.remove('hidden');
            } else {
                btn.disabled = false;
                btn.querySelector('#buttonText').classList.remove('hidden');
                btn.querySelector('#spinner').classList.add('hidden');
            }
        }
    }
    
    function disableTrainingButtons() {
        trainDefaultButton.disabled = true;
        trainUploadedButton.disabled = true;
        fileInput.disabled = true;
        trainDefaultButton.classList.add('opacity-50', 'cursor-not-allowed');
        trainUploadedButton.classList.add('opacity-50'); // Already has disabled style
    }
    
    function enableTrainingButtons() {
        trainDefaultButton.disabled = false;
        trainUploadedButton.disabled = customMedicalData === null;
        fileInput.disabled = false;
        trainDefaultButton.classList.remove('opacity-50', 'cursor-not-allowed');
    }

    function displayResults(probabilities) {
      statusDiv.textContent = "分类结果：";
      resultsDiv.innerHTML = '';

      const maxProb = Math.max(...probabilities);
      const maxIndex = probabilities.indexOf(maxProb);
      const predictedLabel = labels[maxIndex];

      const sortedResults = labels.map((label, i) => ({
        label,
        probability: probabilities[i]
      })).sort((a, b) => b.probability - a.probability);

      sortedResults.forEach(item => {
        const isPredicted = item.label === predictedLabel;
        const resultItem = document.createElement('div');
        resultItem.classList.add('p-4', 'rounded-md', 'shadow-sm', 'transition-all', 'duration-200');
        if (isPredicted) {
          resultItem.classList.add('bg-blue-100', 'border-l-4', 'border-blue-500', 'transform', 'scale-105');
        } else {
          resultItem.classList.add('bg-gray-200');
        }

        resultItem.innerHTML = `
          <div class="font-bold text-gray-800">${item.label}</div>
          <div class="text-sm text-gray-700">${(item.probability * 100).toFixed(2)}%</div>
        `;
        resultsDiv.appendChild(resultItem);
      });
    }

    // --- Event Listeners ---
    trainDefaultButton.addEventListener('click', () => {
        initializeAndTrain(defaultMedicalData);
    });
    
    trainUploadedButton.addEventListener('click', () => {
        if (customMedicalData) {
            initializeAndTrain(customMedicalData);
        } else {
            statusDiv.textContent = "请先上传一个有效的训练文件。";
        }
    });

    classifyButton.addEventListener('click', classifyParagraph);

  </script>
</body>

</html>

