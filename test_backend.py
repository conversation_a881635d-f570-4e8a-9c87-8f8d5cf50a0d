#!/usr/bin/env python3
"""
Test script to verify backend imports and basic functionality
"""

import sys
import os

# Add backend directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_imports():
    """Test that all backend modules can be imported successfully"""
    print("🧪 Testing Backend Imports...")
    
    try:
        print("  ✓ Testing models.document...")
        from models.document import JATSDocument, DocumentResponse, Section, DocumentList
        print("    ✓ JATSDocument imported successfully")
        print("    ✓ DocumentResponse imported successfully") 
        print("    ✓ Section imported successfully")
        print("    ✓ DocumentList imported successfully")
        
        print("  ✓ Testing services.docx_parser...")
        from services.docx_parser import DocxParser
        print("    ✓ DocxParser imported successfully")
        
        print("  ✓ Testing services.ai_service...")
        from services.ai_service import AIService
        print("    ✓ AIService imported successfully")
        
        print("  ✓ Testing services.jats_converter...")
        from services.jats_converter import JATSConverter
        print("    ✓ JATSConverter imported successfully")
        
        print("  ✓ Testing main application...")
        # Don't import main directly as it starts the server
        # Just check if the file exists and is readable
        main_path = os.path.join('backend', 'main.py')
        if os.path.exists(main_path):
            print("    ✓ main.py exists and is accessible")
        else:
            print("    ❌ main.py not found")
            return False
            
        print("\n✅ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"    ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"    ❌ Unexpected error: {e}")
        return False

def test_model_creation():
    """Test that we can create model instances"""
    print("\n🧪 Testing Model Creation...")
    
    try:
        from models.document import Section, DocumentList
        
        # Test DocumentList creation
        doc_list = DocumentList(
            id="test-list",
            type="bullet",
            items=["Item 1", "Item 2", "Item 3"]
        )
        print("  ✓ DocumentList created successfully")
        
        # Test Section creation
        section = Section(
            id="test-section",
            title="Test Section",
            content="This is test content",
            lists=[doc_list]
        )
        print("  ✓ Section with DocumentList created successfully")
        
        print("\n✅ Model creation tests passed!")
        return True
        
    except Exception as e:
        print(f"    ❌ Model creation error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 AME Star Backend Test Suite")
    print("=" * 40)
    
    # Test imports
    imports_ok = test_imports()
    
    if not imports_ok:
        print("\n❌ Import tests failed. Please check dependencies.")
        sys.exit(1)
    
    # Test model creation
    models_ok = test_model_creation()
    
    if not models_ok:
        print("\n❌ Model tests failed.")
        sys.exit(1)
    
    print("\n🎉 All tests passed!")
    print("\n📋 Next steps:")
    print("  1. Install Python dependencies: pip install -r backend/requirements.txt")
    print("  2. Set up environment: cp backend/.env.example backend/.env")
    print("  3. Add OpenAI API key to backend/.env")
    print("  4. Start backend: cd backend && python3 main.py")
    print("  5. Backend will be available at: http://localhost:8000")

if __name__ == "__main__":
    main()
