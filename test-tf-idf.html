<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="UTF-8">
<title>段落类型识别示例</title>
<script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.11.0/dist/tf.min.js"></script>
</head>
<body>

<h1>段落类型识别示例</h1>

<textarea id="text" rows="10" cols="80">
Deep Learning in Medical Image Analysis
Xiaoming Fang1#, Xinyu Zhang2#, Ping Liu1
北京大学 医学院
本文介绍了深度学习在医学图像分析中的应用
Carl <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,5
Department of Neurology, Harvard Medical School
</textarea>
<br/>
<button id="predict">预测段落类型</button>
<pre id="output"></pre>

<script>
(async () => {

  // -----------------------------
  // 1. 扩展词表，覆盖作者、单位、标题常用词
  // -----------------------------
  const vocabulary = [
    '大学','学院','医院','研究所','实验室','中心',
    'Department','Institute','Lab','Medical','Learning','Deep','Cancer','Diagnosis','Novel','Approach',
    'Xiaoming','Fang','Zhang','Liu','Carl','Maki','Ruichen','Yu','Ping','Dan'
  ];

  function textToVector(text) {
    const vec = new Array(vocabulary.length).fill(0);
    const words = text.split(/\s|,|，|\.|#|\^|1|2|3|4|5|6|7|8|9|0/).map(w => w.trim()).filter(Boolean);
    for (const w of words) {
      const idx = vocabulary.indexOf(w);
      if (idx !== -1) vec[idx] = 1;
    }
    return vec;
  }

  // -----------------------------
  // 2. 定义小型 Dense 模型
  // -----------------------------
  const model = tf.sequential();
  model.add(tf.layers.dense({inputShape: [vocabulary.length], units: 32, activation: 'relu'}));
  model.add(tf.layers.dense({units: 16, activation: 'relu'}));
  model.add(tf.layers.dense({units: 4, activation: 'softmax'})); // 4 类
  model.compile({optimizer: 'adam', loss: 'categoricalCrossentropy', metrics: ['accuracy']});

  // -----------------------------
  // 3. 模拟训练数据（可继续扩充）
  // -----------------------------
  const texts = [
    'Deep Learning in Medical Image Analysis', // title
    'A Novel Approach to Cancer Diagnosis',    // title
    'A deep learning approach for predicting visceral pleural invasion in cT1 lung',    // title
    'Xiaoming Fang1#, Xinyu Zhang2#, Ping Liu1', // author
    'Carl G. Maki4, Ruichen Gao1, Ming Liu1,5',  // author
    '北京大学 医学院', // affiliation
    'Department of Neurology, Harvard Medical School', // affiliation
    '本文介绍了深度学习在医学图像分析中的应用', // other
    'This study focuses on medical image segmentation' // other
  ];

  const labels = [
    [1,0,0,0], // title
    [1,0,0,0], // title
    [1,0,0,0], // title
    [0,1,0,0], // author
    [0,1,0,0], // author
    [0,0,1,0], // affiliation
    [0,0,1,0], // affiliation
    [0,0,0,1], // other
    [0,0,0,1]  // other
  ];

  const xs = tf.tensor2d(texts.map(textToVector));
  const ys = tf.tensor2d(labels);

  await model.fit(xs, ys, {epochs: 100, verbose: 0});

  // -----------------------------
  // 4. 作者行正则辅助判断（提高准确率）
  // -----------------------------
  function isAuthorLineRegex(line) {
    const parts = line.split(/[,、;]+/).map(p => p.trim()).filter(Boolean);
    const authorRegex = /^([A-Z][a-z]+(\s[A-Z]\.)?(\s[A-Z][a-z]+)?|[\u4e00-\u9fa5]{2,4})(\d+|\^|#|\*|,?\d+)?$/;
    let matchCount = 0;
    for (const part of parts) {
      if (authorRegex.test(part)) matchCount++;
    }
    return matchCount / parts.length >= 0.7;
  }

  // -----------------------------
  // 5. 预测函数
  // -----------------------------
  function predictParagraphType(line) {
    // 先用正则判断作者行
    if (isAuthorLineRegex(line)) return 'author';

    const inputVec = tf.tensor2d([textToVector(line)]);
    const pred = model.predict(inputVec);
    const idx = pred.argMax(1).dataSync()[0];
    const types = ['title','author','affiliation','other'];
    return types[idx];
  }

  // -----------------------------
  // 6. 按钮事件
  // -----------------------------
  document.getElementById('predict').onclick = () => {
    const lines = document.getElementById('text').value.split('\n').map(l => l.trim()).filter(Boolean);
    const results = lines.map(l => `${predictParagraphType(l)}: ${l}`);
    document.getElementById('output').textContent = results.join('\n');
  };

})();
</script>

</body>
</html>
