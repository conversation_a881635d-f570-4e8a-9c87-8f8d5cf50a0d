from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
from dotenv import load_dotenv
import tempfile
import shutil
from typing import Dict, Any

from services.docx_parser import Docx<PERSON>arser
from services.ai_service import AIService
from services.jats_converter import JATSConverter
from models.document import DocumentResponse, JATSDocument

# Load environment variables
load_dotenv()

app = FastAPI(
    title="AME Star Typesetting Editor API",
    description="AI-assisted typesetting editor with DOCX to JATS XML conversion",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
docx_parser = DocxParser()
ai_service = AIService()
jats_converter = JATSConverter()

@app.get("/")
async def root():
    return {"message": "AME Star Typesetting Editor API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0"}

@app.post("/api/upload-docx", response_model=DocumentResponse)
async def upload_docx(file: UploadFile = File(...)):
    """
    Upload a DOCX file and convert it to JATS XML via AI parsing
    """
    if not file.filename.endswith('.docx'):
        raise HTTPException(status_code=400, detail="File must be a .docx file")
    
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as tmp_file:
            shutil.copyfileobj(file.file, tmp_file)
            tmp_path = tmp_file.name
        
        # Parse DOCX to structured data
        docx_data = await docx_parser.parse_docx(tmp_path)
        
        # Use AI to enhance and structure the parsed data
        ai_enhanced_data = await ai_service.enhance_document_structure(docx_data)
        
        # Convert to JATS XML format
        jats_document = await jats_converter.convert_to_jats(ai_enhanced_data)
        
        # Clean up temporary file
        os.unlink(tmp_path)
        
        return DocumentResponse(
            success=True,
            message="Document processed successfully",
            jats_document=jats_document,
            filename=file.filename
        )
        
    except Exception as e:
        # Clean up temporary file if it exists
        if 'tmp_path' in locals():
            try:
                os.unlink(tmp_path)
            except:
                pass
        
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")

@app.post("/api/convert-to-xml")
async def convert_to_xml(jats_document: JATSDocument):
    """
    Convert JATS document structure to XML string
    """
    try:
        xml_string = await jats_converter.jats_to_xml(jats_document)
        return {"xml": xml_string}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error converting to XML: {str(e)}")

@app.post("/api/convert-to-html")
async def convert_to_html(jats_document: JATSDocument):
    """
    Convert JATS document structure to HTML for preview
    """
    try:
        html_string = await jats_converter.jats_to_html(jats_document)
        return {"html": html_string}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error converting to HTML: {str(e)}")

@app.post("/api/convert-to-pdf")
async def convert_to_pdf(jats_document: JATSDocument):
    """
    Convert JATS document structure to PDF for preview
    """
    try:
        pdf_url = await jats_converter.jats_to_pdf(jats_document)
        return {"pdf_url": pdf_url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error converting to PDF: {str(e)}")

@app.post("/api/ai-enhance")
async def ai_enhance_content(content: Dict[str, Any]):
    """
    Use AI to enhance or fix content parsing gaps
    """
    try:
        enhanced_content = await ai_service.enhance_content(content)
        return {"enhanced_content": enhanced_content}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error enhancing content: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
