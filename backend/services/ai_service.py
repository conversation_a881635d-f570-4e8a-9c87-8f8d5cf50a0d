import openai
import os
import json
from typing import Dict, Any, List
import asyncio

class AIService:
    """
    Service to enhance document structure using AI
    """
    
    def __init__(self):
        self.client = openai.AsyncOpenAI(
            api_key=os.getenv('OPENAI_API_KEY')
        )
    
    async def enhance_document_structure(self, docx_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Use AI to enhance and structure the parsed DOCX data
        """
        try:
            # Create a comprehensive prompt for document enhancement
            prompt = self._create_enhancement_prompt(docx_data)
            
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert academic document processor. Your task is to analyze and enhance document structure for JATS XML conversion. Return only valid JSON."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=4000
            )
            
            # Parse the AI response
            enhanced_data = json.loads(response.choices[0].message.content)
            
            # Merge with original data
            return self._merge_enhanced_data(docx_data, enhanced_data)
            
        except Exception as e:
            # If AI enhancement fails, return original data with basic structure
            print(f"AI enhancement failed: {str(e)}")
            return self._apply_fallback_enhancement(docx_data)
    
    async def enhance_content(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance specific content sections
        """
        try:
            prompt = f"""
            Please enhance and structure the following content for academic publishing:
            
            Content: {json.dumps(content, indent=2)}
            
            Please:
            1. Fix any parsing errors or gaps
            2. Improve formatting and structure
            3. Ensure proper academic tone
            4. Return as structured JSON
            """
            
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert academic editor. Enhance content while preserving original meaning."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.2,
                max_tokens=2000
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            print(f"Content enhancement failed: {str(e)}")
            return content
    
    def _create_enhancement_prompt(self, docx_data: Dict[str, Any]) -> str:
        """
        Create a comprehensive prompt for document enhancement
        """
        return f"""
        Please analyze and enhance this academic document structure for JATS XML conversion:

        Raw Document Data:
        {json.dumps(docx_data, indent=2)}

        Please enhance and return a JSON structure with:

        1. **Title**: Clean, properly formatted article title
        2. **Authors**: Complete author information with:
           - Full names (given_names, surname)
           - Affiliations (extract from document)
           - ORCID IDs if mentioned
           - Corresponding author designation
        3. **Abstract**: Structured with clear sections (Background, Methods, Results, Conclusions)
        4. **Keywords**: Extract or suggest relevant keywords
        5. **Sections**: Properly structured body sections with:
           - Clear hierarchical organization
           - Proper section titles
           - Clean content without formatting artifacts
        6. **References**: Properly formatted reference list
        7. **Figures/Tables**: Enhanced captions and descriptions
        8. **Journal Metadata**: Suggest appropriate journal information

        Focus on:
        - Fixing parsing errors and gaps
        - Improving structure and hierarchy
        - Ensuring JATS XML compatibility
        - Maintaining academic standards

        Return only valid JSON with the enhanced structure.
        """
    
    def _merge_enhanced_data(self, original: Dict[str, Any], enhanced: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge AI-enhanced data with original parsed data
        """
        # Start with original data
        merged = original.copy()
        
        # Override with enhanced data where available
        for key, value in enhanced.items():
            if value:  # Only use non-empty enhanced values
                merged[key] = value
        
        return merged
    
    def _apply_fallback_enhancement(self, docx_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply basic enhancement when AI fails
        """
        enhanced = docx_data.copy()
        
        # Basic title cleaning
        if enhanced.get('title'):
            enhanced['title'] = enhanced['title'].strip()
        
        # Ensure abstract has proper structure
        if not enhanced.get('abstract', {}).get('sections'):
            abstract_content = enhanced.get('abstract', {}).get('content', '')
            if abstract_content:
                enhanced['abstract'] = {
                    'sections': [
                        {
                            'title': 'Abstract',
                            'content': abstract_content
                        }
                    ]
                }
        
        # Ensure sections have proper structure
        if not enhanced.get('sections'):
            enhanced['sections'] = []
        
        return enhanced
