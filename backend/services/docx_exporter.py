import docx
from docx import Document
from docx.shared import <PERSON><PERSON>, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
from typing import Dict, List, Any, Optional
from models.document import JATSDocument
from .content_analyzer import ContentAnalyzer, ContentType

class DocxExporter:
    """
    Service to export JATS documents to DOCX with automatic formatting
    based on content analysis and academic paper conventions
    """
    
    def __init__(self):
        self.content_analyzer = ContentAnalyzer()
        self.content_patterns = {
            'title': [
                r'^(.*?)(?:\s*:\s*.*)?$',  # Main title (before colon if present)
                r'^[A-Z][^.!?]*$',  # Capitalized sentence without ending punctuation
            ],
            'subtitle': [
                r':\s*(.+)$',  # Text after colon in title
            ],
            'abstract_keywords': [
                r'^(abstract|summary|overview)$',
                r'^(keywords?|key\s*words?)$',
                r'^(background|objective|methods?|results?|conclusions?)$',
            ],
            'section_heading': [
                r'^(\d+\.?\s*)(.*)',  # Numbered sections (1. Introduction)
                r'^([A-Z][A-Z\s]+)$',  # ALL CAPS headings
                r'^(introduction|methods?|methodology|results?|discussion|conclusion|references?)$',
                r'^(background|literature\s*review|related\s*work)$',
            ],
            'subsection_heading': [
                r'^(\d+\.\d+\.?\s*)(.*)',  # Numbered subsections (1.1 Overview)
                r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*):?\s*$',  # Title Case headings
            ],
            'figure_caption': [
                r'^(figure|fig\.?)\s*(\d+)[:.]?\s*(.*)',
                r'^(table)\s*(\d+)[:.]?\s*(.*)',
            ],
            'reference_entry': [
                r'^\[\d+\]',  # [1] Reference format
                r'^\d+\.',   # 1. Reference format
                r'^[A-Z][a-z]+,\s*[A-Z]',  # Author, A. format
            ],
            'equation': [
                r'.*[\(\[]?\s*\d+\s*[\)\]]?\s*$',  # Equations with numbers
                r'.*=.*',  # Contains equals sign
            ]
        }
        
        self.format_styles = self._define_format_styles()
    
    def _define_format_styles(self) -> Dict[str, Dict[str, Any]]:
        """Define formatting styles for different content types"""
        return {
            'title': {
                'font_name': 'Times New Roman',
                'font_size': 16,
                'bold': True,
                'alignment': WD_ALIGN_PARAGRAPH.CENTER,
                'space_after': Pt(12),
                'space_before': Pt(0),
            },
            'subtitle': {
                'font_name': 'Times New Roman',
                'font_size': 14,
                'bold': False,
                'italic': True,
                'alignment': WD_ALIGN_PARAGRAPH.CENTER,
                'space_after': Pt(18),
                'space_before': Pt(6),
            },
            'author': {
                'font_name': 'Times New Roman',
                'font_size': 12,
                'bold': False,
                'alignment': WD_ALIGN_PARAGRAPH.CENTER,
                'space_after': Pt(6),
                'space_before': Pt(0),
            },
            'affiliation': {
                'font_name': 'Times New Roman',
                'font_size': 10,
                'bold': False,
                'italic': True,
                'alignment': WD_ALIGN_PARAGRAPH.CENTER,
                'space_after': Pt(12),
                'space_before': Pt(0),
            },
            'abstract_heading': {
                'font_name': 'Times New Roman',
                'font_size': 12,
                'bold': True,
                'alignment': WD_ALIGN_PARAGRAPH.LEFT,
                'space_after': Pt(6),
                'space_before': Pt(12),
            },
            'abstract_body': {
                'font_name': 'Times New Roman',
                'font_size': 11,
                'bold': False,
                'alignment': WD_ALIGN_PARAGRAPH.JUSTIFY,
                'space_after': Pt(12),
                'space_before': Pt(0),
                'line_spacing': 1.15,
                'first_line_indent': Inches(0),
            },
            'keywords': {
                'font_name': 'Times New Roman',
                'font_size': 11,
                'bold': False,
                'italic': True,
                'alignment': WD_ALIGN_PARAGRAPH.LEFT,
                'space_after': Pt(18),
                'space_before': Pt(6),
            },
            'section_heading': {
                'font_name': 'Times New Roman',
                'font_size': 14,
                'bold': True,
                'alignment': WD_ALIGN_PARAGRAPH.LEFT,
                'space_after': Pt(6),
                'space_before': Pt(18),
            },
            'subsection_heading': {
                'font_name': 'Times New Roman',
                'font_size': 12,
                'bold': True,
                'alignment': WD_ALIGN_PARAGRAPH.LEFT,
                'space_after': Pt(6),
                'space_before': Pt(12),
            },
            'body_text': {
                'font_name': 'Times New Roman',
                'font_size': 12,
                'bold': False,
                'alignment': WD_ALIGN_PARAGRAPH.JUSTIFY,
                'space_after': Pt(6),
                'space_before': Pt(0),
                'line_spacing': 1.5,
                'first_line_indent': Inches(0.5),
            },
            'figure_caption': {
                'font_name': 'Times New Roman',
                'font_size': 10,
                'bold': False,
                'italic': True,
                'alignment': WD_ALIGN_PARAGRAPH.CENTER,
                'space_after': Pt(12),
                'space_before': Pt(6),
            },
            'table_caption': {
                'font_name': 'Times New Roman',
                'font_size': 10,
                'bold': True,
                'alignment': WD_ALIGN_PARAGRAPH.CENTER,
                'space_after': Pt(6),
                'space_before': Pt(12),
            },
            'reference_heading': {
                'font_name': 'Times New Roman',
                'font_size': 14,
                'bold': True,
                'alignment': WD_ALIGN_PARAGRAPH.LEFT,
                'space_after': Pt(12),
                'space_before': Pt(24),
            },
            'reference_entry': {
                'font_name': 'Times New Roman',
                'font_size': 11,
                'bold': False,
                'alignment': WD_ALIGN_PARAGRAPH.LEFT,
                'space_after': Pt(6),
                'space_before': Pt(0),
                'hanging_indent': Inches(0.5),
            },
            'equation': {
                'font_name': 'Times New Roman',
                'font_size': 12,
                'bold': False,
                'alignment': WD_ALIGN_PARAGRAPH.CENTER,
                'space_after': Pt(12),
                'space_before': Pt(12),
            }
        }

    async def export_to_docx(self, jats_document: JATSDocument, format_style: str = 'academic') -> bytes:
        """
        Export JATS document to DOCX with automatic formatting

        Args:
            jats_document: The JATS document to export
            format_style: The formatting style to apply ('academic', 'journal', 'manuscript')

        Returns:
            bytes: The DOCX file content as bytes
        """
        try:
            # Create new document
            doc = Document()

            # Set document margins and page setup
            self._setup_document_margins(doc)

            # Create custom styles
            self._create_custom_styles(doc)

            # Analyze and format front matter
            await self._format_front_matter(doc, jats_document.front)

            # Analyze and format body content
            await self._format_body_content(doc, jats_document.body)

            # Analyze and format back matter
            await self._format_back_matter(doc, jats_document.back)

            # Save to bytes
            from io import BytesIO
            doc_bytes = BytesIO()
            doc.save(doc_bytes)
            doc_bytes.seek(0)

            return doc_bytes.getvalue()

        except Exception as e:
            raise Exception(f"Error exporting to DOCX: {str(e)}")

    def _setup_document_margins(self, doc: Document):
        """Set up document margins for academic paper format"""
        sections = doc.sections
        for section in sections:
            section.top_margin = Inches(1.0)
            section.bottom_margin = Inches(1.0)
            section.left_margin = Inches(1.0)
            section.right_margin = Inches(1.0)

    def _create_custom_styles(self, doc: Document):
        """Create custom styles for the document"""
        styles = doc.styles

        for style_name, style_props in self.format_styles.items():
            try:
                # Try to get existing style or create new one
                try:
                    style = styles[style_name]
                except KeyError:
                    style = styles.add_style(style_name, WD_STYLE_TYPE.PARAGRAPH)

                # Apply formatting properties
                self._apply_style_properties(style, style_props)

            except Exception as e:
                print(f"Warning: Could not create style {style_name}: {str(e)}")

    def _apply_style_properties(self, style, props: Dict[str, Any]):
        """Apply formatting properties to a style"""
        paragraph_format = style.paragraph_format
        font = style.font

        # Font properties
        if 'font_name' in props:
            font.name = props['font_name']
        if 'font_size' in props:
            font.size = Pt(props['font_size'])
        if 'bold' in props:
            font.bold = props['bold']
        if 'italic' in props:
            font.italic = props['italic']

        # Paragraph properties
        if 'alignment' in props:
            paragraph_format.alignment = props['alignment']
        if 'space_after' in props:
            paragraph_format.space_after = props['space_after']
        if 'space_before' in props:
            paragraph_format.space_before = props['space_before']
        if 'line_spacing' in props:
            paragraph_format.line_spacing = props['line_spacing']
        if 'first_line_indent' in props:
            paragraph_format.first_line_indent = props['first_line_indent']
        if 'hanging_indent' in props:
            paragraph_format.left_indent = props['hanging_indent']
            paragraph_format.first_line_indent = -props['hanging_indent']

    async def _format_front_matter(self, doc: Document, front_matter: Dict[str, Any]):
        """Format the front matter (title, authors, abstract, etc.)"""
        article_meta = front_matter.get('article_meta', {})

        # Title
        title_group = article_meta.get('title_group', {})
        if title_group.get('article_title'):
            title_text = title_group['article_title']

            # Check for subtitle (text after colon)
            if ':' in title_text:
                main_title, subtitle = title_text.split(':', 1)
                self._add_formatted_paragraph(doc, main_title.strip(), 'title')
                self._add_formatted_paragraph(doc, subtitle.strip(), 'subtitle')
            else:
                self._add_formatted_paragraph(doc, title_text, 'title')

        # Authors
        contributors = article_meta.get('contributors', [])
        if contributors:
            author_names = []
            for contributor in contributors:
                if contributor.get('type') == 'author':
                    name_parts = []
                    if contributor.get('given_names'):
                        name_parts.append(contributor['given_names'])
                    if contributor.get('surname'):
                        name_parts.append(contributor['surname'])
                    if name_parts:
                        author_names.append(' '.join(name_parts))

            if author_names:
                authors_text = ', '.join(author_names)
                self._add_formatted_paragraph(doc, authors_text, 'author')

        # Affiliations
        affiliations = article_meta.get('affiliations', [])
        if affiliations:
            for affiliation in affiliations:
                if affiliation.get('institution'):
                    self._add_formatted_paragraph(doc, affiliation['institution'], 'affiliation')

        # Abstract
        abstract = article_meta.get('abstract', {})
        if abstract.get('sections'):
            self._add_formatted_paragraph(doc, 'Abstract', 'abstract_heading')

            for section in abstract['sections']:
                if section.get('content'):
                    content = section['content'].strip()
                    if content:
                        self._add_formatted_paragraph(doc, content, 'abstract_body')

        # Keywords
        keyword_groups = article_meta.get('keyword_groups', [])
        if keyword_groups:
            for group in keyword_groups:
                keywords = group.get('keywords', [])
                if keywords:
                    keyword_text = 'Keywords: ' + ', '.join(keywords)
                    self._add_formatted_paragraph(doc, keyword_text, 'keywords')

    async def _format_body_content(self, doc: Document, body: Dict[str, Any]):
        """Format the body content with automatic section detection"""
        sections = body.get('sections', [])

        for section in sections:
            # Format section title
            if section.get('title'):
                title = section['title'].strip()
                analysis = self.content_analyzer.analyze_content(title)

                if analysis.content_type == ContentType.SUBSECTION_HEADING:
                    style = 'subsection_heading'
                else:
                    style = 'section_heading'

                self._add_formatted_paragraph(doc, title, style)

            # Format section content
            if section.get('content'):
                content = section['content'].strip()
                if content:
                    # Split content into paragraphs
                    paragraphs = content.split('\n\n')

                    for paragraph in paragraphs:
                        paragraph = paragraph.strip()
                        if paragraph:
                            # Analyze paragraph type
                            analysis = self.content_analyzer.analyze_content(paragraph)

                            if analysis.content_type == ContentType.EQUATION:
                                self._add_formatted_paragraph(doc, paragraph, 'equation')
                            elif analysis.content_type == ContentType.FIGURE_CAPTION:
                                self._add_formatted_paragraph(doc, paragraph, 'figure_caption')
                            else:
                                self._add_formatted_paragraph(doc, paragraph, 'body_text')

            # Format figures
            if section.get('figures'):
                for figure in section['figures']:
                    if figure.get('caption'):
                        caption = f"Figure {figure.get('position', '')}: {figure['caption']}"
                        self._add_formatted_paragraph(doc, caption, 'figure_caption')

            # Format tables
            if section.get('tables'):
                for table in section['tables']:
                    if table.get('caption'):
                        caption = f"Table {table.get('position', '')}: {table['caption']}"
                        self._add_formatted_paragraph(doc, caption, 'table_caption')

            # Format subsections recursively
            if section.get('subsections'):
                await self._format_subsections(doc, section['subsections'])

    async def _format_subsections(self, doc: Document, subsections: List[Dict[str, Any]]):
        """Format subsections recursively"""
        for subsection in subsections:
            if subsection.get('title'):
                title = subsection['title'].strip()
                self._add_formatted_paragraph(doc, title, 'subsection_heading')

            if subsection.get('content'):
                content = subsection['content'].strip()
                if content:
                    paragraphs = content.split('\n\n')
                    for paragraph in paragraphs:
                        paragraph = paragraph.strip()
                        if paragraph:
                            analysis = self.content_analyzer.analyze_content(paragraph)
                            if analysis.content_type == ContentType.EQUATION:
                                self._add_formatted_paragraph(doc, paragraph, 'equation')
                            else:
                                self._add_formatted_paragraph(doc, paragraph, 'body_text')

            # Recursive call for nested subsections
            if subsection.get('subsections'):
                await self._format_subsections(doc, subsection['subsections'])

    async def _format_back_matter(self, doc: Document, back_matter: Dict[str, Any]):
        """Format the back matter (references, appendices, etc.)"""
        references = back_matter.get('references', [])

        if references:
            # Add references heading
            self._add_formatted_paragraph(doc, 'References', 'reference_heading')

            # Format each reference
            for i, reference in enumerate(references, 1):
                if reference.get('citation'):
                    # Format reference with number
                    ref_text = f"{i}. {reference['citation']}"
                    self._add_formatted_paragraph(doc, ref_text, 'reference_entry')
                elif reference.get('mixed_citation'):
                    ref_text = f"{i}. {reference['mixed_citation']}"
                    self._add_formatted_paragraph(doc, ref_text, 'reference_entry')

    def get_enhanced_analysis(self, text: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Get enhanced content analysis using the ContentAnalyzer

        Args:
            text: The text content to analyze
            context: Optional context information

        Returns:
            str: The suggested style name
        """
        analysis = self.content_analyzer.analyze_content(text, context)
        return analysis.suggested_style

    def _add_formatted_paragraph(self, doc: Document, text: str, style_name: str):
        """
        Add a paragraph with specified formatting style

        Args:
            doc: The document to add the paragraph to
            text: The text content
            style_name: The style to apply
        """
        try:
            paragraph = doc.add_paragraph(text)

            # Try to apply custom style if it exists
            try:
                paragraph.style = doc.styles[style_name]
            except KeyError:
                # Fallback to manual formatting if style doesn't exist
                self._apply_manual_formatting(paragraph, style_name)

        except Exception as e:
            # Fallback: add paragraph with default formatting
            doc.add_paragraph(text)
            print(f"Warning: Could not apply formatting for style {style_name}: {str(e)}")

    def _apply_manual_formatting(self, paragraph, style_name: str):
        """Apply formatting manually if custom style is not available"""
        if style_name not in self.format_styles:
            return

        props = self.format_styles[style_name]

        # Apply paragraph formatting
        if 'alignment' in props:
            paragraph.alignment = props['alignment']
        if 'space_after' in props:
            paragraph.paragraph_format.space_after = props['space_after']
        if 'space_before' in props:
            paragraph.paragraph_format.space_before = props['space_before']
        if 'line_spacing' in props:
            paragraph.paragraph_format.line_spacing = props['line_spacing']
        if 'first_line_indent' in props:
            paragraph.paragraph_format.first_line_indent = props['first_line_indent']

        # Apply font formatting to all runs
        for run in paragraph.runs:
            if 'font_name' in props:
                run.font.name = props['font_name']
            if 'font_size' in props:
                run.font.size = Pt(props['font_size'])
            if 'bold' in props:
                run.font.bold = props['bold']
            if 'italic' in props:
                run.font.italic = props['italic']
