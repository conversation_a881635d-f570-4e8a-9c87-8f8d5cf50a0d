from typing import Dict, Any
from models.document import JATSDocument
import xml.etree.ElementTree as ET
from xml.dom import minidom
import json

class JATSConverter:
    """
    Service to convert enhanced document data to JATS XML format
    """
    
    def __init__(self):
        self.jats_dtd = 'http://jats.nlm.nih.gov/publishing/1.2/JATS-journalpublishing1.dtd'
    
    async def convert_to_jats(self, enhanced_data: Dict[str, Any]) -> JATSDocument:
        """
        Convert enhanced document data to JATS document structure
        """
        try:
            # Create JATS document structure
            jats_doc = JATSDocument(
                article_type="research-article",
                dtd_version="1.2",
                language="en",
                front=self._create_front_matter(enhanced_data),
                body=self._create_body(enhanced_data),
                back=self._create_back_matter(enhanced_data)
            )
            
            return jats_doc
            
        except Exception as e:
            raise Exception(f"Error converting to JATS: {str(e)}")
    
    async def jats_to_xml(self, jats_document: JATSDocument) -> str:
        """
        Convert JATS document structure to XML string
        """
        try:
            # Create root article element
            article = ET.Element('article')
            article.set('article-type', jats_document.article_type)
            article.set('dtd-version', jats_document.dtd_version)
            article.set('xml:lang', jats_document.language)
            article.set('xmlns:mml', 'http://www.w3.org/1998/Math/MathML')
            article.set('xmlns:xlink', 'http://www.w3.org/1999/xlink')
            
            # Add front matter
            front = ET.SubElement(article, 'front')
            self._add_journal_meta(front, jats_document.front.get('journal_meta', {}))
            self._add_article_meta(front, jats_document.front.get('article_meta', {}))
            
            # Add body
            body = ET.SubElement(article, 'body')
            self._add_body_sections(body, jats_document.body.sections)
            
            # Add back matter
            back = ET.SubElement(article, 'back')
            self._add_references(back, jats_document.back.references)
            
            # Convert to pretty-printed XML string
            rough_string = ET.tostring(article, encoding='unicode')
            reparsed = minidom.parseString(rough_string)
            
            # Add DOCTYPE declaration
            doctype = f'<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "{self.jats_dtd}">'
            xml_declaration = '<?xml version="1.0" encoding="UTF-8" standalone="no"?>'
            
            pretty_xml = reparsed.toprettyxml(indent="  ")
            # Remove the auto-generated XML declaration and add our custom one
            lines = pretty_xml.split('\n')[1:]  # Remove first line
            final_xml = xml_declaration + doctype + '\n' + '\n'.join(lines)
            
            return final_xml
            
        except Exception as e:
            raise Exception(f"Error converting JATS to XML: {str(e)}")
    
    async def jats_to_html(self, jats_document: JATSDocument) -> str:
        """
        Convert JATS document to HTML for preview
        """
        try:
            html_parts = []
            
            # HTML header
            html_parts.append("""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Article Preview</title>
                <style>
                    body { font-family: 'Times New Roman', serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
                    .title { font-size: 24px; font-weight: bold; margin-bottom: 20px; text-align: center; }
                    .authors { text-align: center; margin-bottom: 20px; font-style: italic; }
                    .abstract { background: #f5f5f5; padding: 15px; margin: 20px 0; border-left: 4px solid #007acc; }
                    .section { margin: 20px 0; }
                    .section-title { font-size: 18px; font-weight: bold; margin: 15px 0 10px 0; }
                    .keywords { margin: 15px 0; font-style: italic; }
                    .references { font-size: 14px; }
                    .reference { margin: 5px 0; }
                </style>
            </head>
            <body>
            """)
            
            # Article title
            article_meta = jats_document.front.get('article_meta', {})
            title = article_meta.get('title_group', {}).get('article_title', '')
            html_parts.append(f'<div class="title">{title}</div>')
            
            # Authors
            authors = article_meta.get('contributors', [])
            if authors:
                author_names = []
                for author in authors:
                    name = author.get('name', {})
                    full_name = f"{name.get('given_names', '')} {name.get('surname', '')}"
                    author_names.append(full_name.strip())
                html_parts.append(f'<div class="authors">{", ".join(author_names)}</div>')
            
            # Abstract
            abstract = article_meta.get('abstract', {})
            if abstract.get('sections'):
                html_parts.append('<div class="abstract">')
                html_parts.append('<h3>Abstract</h3>')
                for section in abstract['sections']:
                    if section.get('title') and section['title'].lower() != 'abstract':
                        html_parts.append(f'<strong>{section["title"]}:</strong> ')
                    html_parts.append(f'{section.get("content", "")}<br><br>')
                html_parts.append('</div>')
            
            # Keywords
            keyword_groups = article_meta.get('keyword_groups', [])
            if keyword_groups:
                for group in keyword_groups:
                    keywords = [kw.get('value', '') for kw in group.get('keywords', [])]
                    if keywords:
                        html_parts.append(f'<div class="keywords"><strong>Keywords:</strong> {", ".join(keywords)}</div>')
            
            # Body sections
            for section in jats_document.body.sections:
                html_parts.append(self._section_to_html(section))
            
            # References
            if jats_document.back.references:
                html_parts.append('<div class="section"><div class="section-title">References</div>')
                html_parts.append('<div class="references">')
                for i, ref in enumerate(jats_document.back.references, 1):
                    html_parts.append(f'<div class="reference">{i}. {ref.get("content", "")}</div>')
                html_parts.append('</div></div>')
            
            # HTML footer
            html_parts.append('</body></html>')
            
            return ''.join(html_parts)
            
        except Exception as e:
            raise Exception(f"Error converting JATS to HTML: {str(e)}")
    
    async def jats_to_pdf(self, jats_document: JATSDocument) -> str:
        """
        Convert JATS document to PDF (returns URL or path)
        """
        # For now, return a placeholder
        # In a real implementation, you would use a library like WeasyPrint or similar
        return "/api/pdf-preview/placeholder.pdf"

    def _create_front_matter(self, enhanced_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create front matter from enhanced data"""
        return {
            'journal_meta': self._create_journal_meta(enhanced_data),
            'article_meta': self._create_article_meta(enhanced_data)
        }

    def _create_journal_meta(self, enhanced_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create journal metadata"""
        return {
            'journal_ids': [
                {'type': 'publisher-id', 'value': 'AME-STAR'},
                {'type': 'nlm-ta', 'value': 'AME Star J'}
            ],
            'journal_title': 'AME Star Journal',
            'abbrev_journal_title': 'AME Star J.',
            'publisher': {'name': 'AME Publishing Company'}
        }

    def _create_article_meta(self, enhanced_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create article metadata from enhanced data"""
        return {
            'article_ids': [
                {'type': 'publisher-id', 'value': 'ame-star-001'},
                {'type': 'doi', 'value': '10.21037/ame-star-001'}
            ],
            'article_categories': [
                {'type': 'heading', 'subject': 'Original Article'}
            ],
            'title_group': {
                'article_title': enhanced_data.get('title', '')
            },
            'contributors': self._convert_authors(enhanced_data.get('authors', [])),
            'affiliations': [],
            'author_notes': [],
            'correspondences': [],
            'pub_dates': [],
            'volume': '1',
            'issue': '1',
            'first_page': '1',
            'last_page': '10',
            'history': [],
            'permissions': {
                'copyright_statement': 'Copyright © 2024 AME Publishing Company. All rights reserved.',
                'copyright_year': '2024',
                'copyright_holder': 'AME Publishing Company.',
                'license': {
                    'href': 'http://creativecommons.org/licenses/by-nc-nd/4.0/',
                    'content': 'Open Access Statement'
                }
            },
            'abstract': enhanced_data.get('abstract', {'sections': []}),
            'keyword_groups': self._convert_keywords(enhanced_data.get('keywords', [])),
            'funding_groups': [],
            'custom_meta': []
        }

    def _create_body(self, enhanced_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create body from enhanced data"""
        return {
            'sections': enhanced_data.get('sections', [])
        }

    def _create_back_matter(self, enhanced_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create back matter from enhanced data"""
        return {
            'references': enhanced_data.get('references', [])
        }

    def _convert_authors(self, authors: list) -> list:
        """Convert author data to JATS format"""
        converted = []
        for author in authors:
            converted.append({
                'type': 'author',
                'name': {
                    'surname': author.get('surname', ''),
                    'given_names': author.get('given_names', '')
                },
                'affiliation_refs': author.get('affiliations', []),
                'is_corresponding': author.get('is_corresponding', False)
            })
        return converted

    def _convert_keywords(self, keywords: list) -> list:
        """Convert keywords to JATS format"""
        if not keywords:
            return []

        return [{
            'type': 'author',
            'title': 'Keywords:',
            'keywords': [{'value': kw} for kw in keywords]
        }]

    def _add_journal_meta(self, front_elem, journal_meta: Dict[str, Any]):
        """Add journal metadata to front element"""
        j_meta = ET.SubElement(front_elem, 'journal-meta')

        # Journal IDs
        for j_id in journal_meta.get('journal_ids', []):
            j_id_elem = ET.SubElement(j_meta, 'journal-id')
            j_id_elem.set('journal-id-type', j_id['type'])
            j_id_elem.text = j_id['value']

        # Journal title
        title_group = ET.SubElement(j_meta, 'journal-title-group')
        j_title = ET.SubElement(title_group, 'journal-title')
        j_title.text = journal_meta.get('journal_title', '')

        # Publisher
        publisher = ET.SubElement(j_meta, 'publisher')
        pub_name = ET.SubElement(publisher, 'publisher-name')
        pub_name.text = journal_meta.get('publisher', {}).get('name', '')

    def _add_article_meta(self, front_elem, article_meta: Dict[str, Any]):
        """Add article metadata to front element"""
        a_meta = ET.SubElement(front_elem, 'article-meta')

        # Article IDs
        for a_id in article_meta.get('article_ids', []):
            a_id_elem = ET.SubElement(a_meta, 'article-id')
            a_id_elem.set('pub-id-type', a_id['type'])
            a_id_elem.text = a_id['value']

        # Title
        title_group = ET.SubElement(a_meta, 'title-group')
        a_title = ET.SubElement(title_group, 'article-title')
        a_title.text = article_meta.get('title_group', {}).get('article_title', '')

        # Abstract
        abstract_data = article_meta.get('abstract', {})
        if abstract_data.get('sections'):
            abstract_elem = ET.SubElement(a_meta, 'abstract')
            for section in abstract_data['sections']:
                sec_elem = ET.SubElement(abstract_elem, 'sec')
                title_elem = ET.SubElement(sec_elem, 'title')
                title_elem.text = section.get('title', '')
                p_elem = ET.SubElement(sec_elem, 'p')
                p_elem.text = section.get('content', '')

    def _add_body_sections(self, body_elem, sections: list):
        """Add body sections to body element"""
        for section in sections:
            sec_elem = ET.SubElement(body_elem, 'sec')
            if section.get('id'):
                sec_elem.set('id', section['id'])
            if section.get('type'):
                sec_elem.set('sec-type', section['type'])

            # Section title
            title_elem = ET.SubElement(sec_elem, 'title')
            title_elem.text = section.get('title', '')

            # Section content
            if section.get('content'):
                p_elem = ET.SubElement(sec_elem, 'p')
                p_elem.text = section['content']

            # Subsections
            if section.get('subsections'):
                self._add_body_sections(sec_elem, section['subsections'])

    def _add_references(self, back_elem, references: list):
        """Add references to back element"""
        if not references:
            return

        ref_list = ET.SubElement(back_elem, 'ref-list')
        for ref in references:
            ref_elem = ET.SubElement(ref_list, 'ref')
            if ref.get('id'):
                ref_elem.set('id', ref['id'])

            element_citation = ET.SubElement(ref_elem, 'element-citation')
            element_citation.text = ref.get('content', '')

    def _section_to_html(self, section: Dict[str, Any]) -> str:
        """Convert a section to HTML"""
        html = f'<div class="section">'
        html += f'<div class="section-title">{section.get("title", "")}</div>'
        html += f'<div>{section.get("content", "")}</div>'

        # Add subsections
        if section.get('subsections'):
            for subsection in section['subsections']:
                html += self._section_to_html(subsection)

        html += '</div>'
        return html
