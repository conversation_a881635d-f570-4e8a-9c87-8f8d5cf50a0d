import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class ContentType(Enum):
    """Enumeration of content types for formatting"""
    TITLE = "title"
    SUBTITLE = "subtitle"
    AUTHOR = "author"
    AFFILIATION = "affiliation"
    ABSTRACT_HEADING = "abstract_heading"
    ABSTRACT_BODY = "abstract_body"
    KEYWORDS = "keywords"
    SECTION_HEADING = "section_heading"
    SUBSECTION_HEADING = "subsection_heading"
    SUBSUBSECTION_HEADING = "subsubsection_heading"
    BODY_TEXT = "body_text"
    FIGURE_CAPTION = "figure_caption"
    TABLE_CAPTION = "table_caption"
    EQUATION = "equation"
    REFERENCE_HEADING = "reference_heading"
    REFERENCE_ENTRY = "reference_entry"
    FOOTNOTE = "footnote"
    QUOTE = "quote"
    LIST_ITEM = "list_item"
    CODE_BLOCK = "code_block"

@dataclass
class ContentAnalysis:
    """Result of content analysis"""
    content_type: ContentType
    confidence: float
    metadata: Dict[str, Any]
    suggested_style: str

class ContentAnalyzer:
    """
    Advanced content analysis system for automatic document formatting
    """
    
    def __init__(self):
        self.patterns = self._initialize_patterns()
        self.academic_keywords = self._initialize_academic_keywords()
        self.formatting_rules = self._initialize_formatting_rules()
    
    def _initialize_patterns(self) -> Dict[ContentType, List[Dict[str, Any]]]:
        """Initialize regex patterns for content type detection"""
        return {
            ContentType.TITLE: [
                {
                    'pattern': r'^[A-Z][^.!?]*(?:[:.][^.!?]*)?$',
                    'weight': 0.8,
                    'description': 'Capitalized text without ending punctuation'
                },
                {
                    'pattern': r'^.{10,100}$',
                    'weight': 0.6,
                    'description': 'Moderate length text (typical title length)'
                },
                {
                    'pattern': r'^(?!.*\b(?:introduction|methods?|results?|discussion|conclusion)\b).*',
                    'weight': 0.7,
                    'description': 'Not a common section heading'
                }
            ],
            ContentType.SUBTITLE: [
                {
                    'pattern': r':\s*(.+)$',
                    'weight': 0.9,
                    'description': 'Text after colon (subtitle indicator)'
                },
                {
                    'pattern': r'^[a-z].*',
                    'weight': 0.6,
                    'description': 'Starts with lowercase (subtitle style)'
                }
            ],
            ContentType.SECTION_HEADING: [
                {
                    'pattern': r'^(\d+\.?\s*)(.*)',
                    'weight': 0.9,
                    'description': 'Numbered section (1. Introduction)'
                },
                {
                    'pattern': r'^(introduction|methods?|methodology|results?|discussion|conclusion|references?)$',
                    'weight': 0.95,
                    'description': 'Standard academic section names'
                },
                {
                    'pattern': r'^([A-Z][A-Z\s]+)$',
                    'weight': 0.8,
                    'description': 'ALL CAPS heading'
                },
                {
                    'pattern': r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*):?\s*$',
                    'weight': 0.7,
                    'description': 'Title Case heading'
                }
            ],
            ContentType.SUBSECTION_HEADING: [
                {
                    'pattern': r'^(\d+\.\d+\.?\s*)(.*)',
                    'weight': 0.9,
                    'description': 'Numbered subsection (1.1 Overview)'
                },
                {
                    'pattern': r'^(\d+\.\d+\.\d+\.?\s*)(.*)',
                    'weight': 0.85,
                    'description': 'Numbered subsubsection (1.1.1 Details)'
                }
            ],
            ContentType.ABSTRACT_BODY: [
                {
                    'pattern': r'^(background|objective|methods?|results?|conclusions?)[:.]?\s*',
                    'weight': 0.8,
                    'description': 'Abstract section indicators'
                }
            ],
            ContentType.FIGURE_CAPTION: [
                {
                    'pattern': r'^(figure|fig\.?)\s*(\d+)[:.]?\s*(.*)',
                    'weight': 0.95,
                    'description': 'Figure caption format'
                }
            ],
            ContentType.TABLE_CAPTION: [
                {
                    'pattern': r'^(table)\s*(\d+)[:.]?\s*(.*)',
                    'weight': 0.95,
                    'description': 'Table caption format'
                }
            ],
            ContentType.REFERENCE_ENTRY: [
                {
                    'pattern': r'^\[\d+\]',
                    'weight': 0.9,
                    'description': '[1] Reference format'
                },
                {
                    'pattern': r'^\d+\.',
                    'weight': 0.8,
                    'description': '1. Reference format'
                },
                {
                    'pattern': r'^[A-Z][a-z]+,\s*[A-Z]',
                    'weight': 0.85,
                    'description': 'Author, A. format'
                },
                {
                    'pattern': r'\(\d{4}\)',
                    'weight': 0.7,
                    'description': 'Contains year in parentheses'
                }
            ],
            ContentType.EQUATION: [
                {
                    'pattern': r'.*[\(\[]?\s*\d+\s*[\)\]]?\s*$',
                    'weight': 0.8,
                    'description': 'Equation with number'
                },
                {
                    'pattern': r'.*=.*',
                    'weight': 0.7,
                    'description': 'Contains equals sign'
                },
                {
                    'pattern': r'.*[∫∑∏√±≤≥≠∞∂∇].*',
                    'weight': 0.9,
                    'description': 'Contains mathematical symbols'
                }
            ],
            ContentType.KEYWORDS: [
                {
                    'pattern': r'^keywords?\s*[:.]?\s*',
                    'weight': 0.95,
                    'description': 'Keywords indicator'
                }
            ],
            ContentType.QUOTE: [
                {
                    'pattern': r'^[""].*[""]$',
                    'weight': 0.8,
                    'description': 'Text in quotes'
                },
                {
                    'pattern': r'^\s*[">]',
                    'weight': 0.7,
                    'description': 'Starts with quote indicator'
                }
            ],
            ContentType.LIST_ITEM: [
                {
                    'pattern': r'^\s*[-•*]\s+',
                    'weight': 0.9,
                    'description': 'Bullet point'
                },
                {
                    'pattern': r'^\s*\d+[.)]\s+',
                    'weight': 0.9,
                    'description': 'Numbered list item'
                },
                {
                    'pattern': r'^\s*[a-z][.)]\s+',
                    'weight': 0.8,
                    'description': 'Lettered list item'
                }
            ],
            ContentType.CODE_BLOCK: [
                {
                    'pattern': r'```.*```',
                    'weight': 0.95,
                    'description': 'Code block markers'
                },
                {
                    'pattern': r'^\s{4,}',
                    'weight': 0.6,
                    'description': 'Indented code'
                }
            ]
        }
    
    def _initialize_academic_keywords(self) -> Dict[str, List[str]]:
        """Initialize academic keywords for context analysis"""
        return {
            'methodology': [
                'method', 'methodology', 'approach', 'technique', 'procedure',
                'protocol', 'experiment', 'analysis', 'measurement', 'evaluation'
            ],
            'results': [
                'result', 'finding', 'outcome', 'data', 'observation',
                'measurement', 'value', 'statistic', 'correlation', 'significance'
            ],
            'discussion': [
                'discussion', 'interpretation', 'implication', 'limitation',
                'future work', 'conclusion', 'summary', 'recommendation'
            ],
            'academic_terms': [
                'hypothesis', 'theory', 'model', 'framework', 'literature',
                'research', 'study', 'investigation', 'survey', 'review'
            ]
        }
    
    def _initialize_formatting_rules(self) -> Dict[str, Dict[str, Any]]:
        """Initialize formatting rules based on content analysis"""
        return {
            'academic_paper': {
                'title_max_length': 150,
                'abstract_max_length': 300,
                'section_numbering': True,
                'reference_style': 'numbered',
                'figure_numbering': True,
                'table_numbering': True
            },
            'journal_article': {
                'title_max_length': 100,
                'abstract_max_length': 250,
                'section_numbering': False,
                'reference_style': 'author_year',
                'figure_numbering': True,
                'table_numbering': True
            },
            'manuscript': {
                'title_max_length': 200,
                'abstract_max_length': 500,
                'section_numbering': True,
                'reference_style': 'numbered',
                'figure_numbering': True,
                'table_numbering': True
            }
        }
    
    def analyze_content(self, text: str, context: Optional[Dict[str, Any]] = None) -> ContentAnalysis:
        """
        Analyze text content to determine its type and appropriate formatting
        
        Args:
            text: The text content to analyze
            context: Optional context information (position, surrounding content, etc.)
            
        Returns:
            ContentAnalysis: Analysis result with content type and formatting suggestions
        """
        text = text.strip()
        if not text:
            return ContentAnalysis(
                content_type=ContentType.BODY_TEXT,
                confidence=0.0,
                metadata={},
                suggested_style='body_text'
            )
        
        # Calculate scores for each content type
        type_scores = {}
        
        for content_type, patterns in self.patterns.items():
            score = self._calculate_type_score(text, patterns, context)
            if score > 0:
                type_scores[content_type] = score
        
        # Find the best match
        if type_scores:
            best_type = max(type_scores.keys(), key=lambda k: type_scores[k])
            confidence = type_scores[best_type]
        else:
            best_type = ContentType.BODY_TEXT
            confidence = 0.5
        
        # Generate metadata
        metadata = self._generate_metadata(text, best_type, context)
        
        # Suggest formatting style
        suggested_style = self._suggest_style(best_type, metadata)
        
        return ContentAnalysis(
            content_type=best_type,
            confidence=confidence,
            metadata=metadata,
            suggested_style=suggested_style
        )

    def _calculate_type_score(self, text: str, patterns: List[Dict[str, Any]], context: Optional[Dict[str, Any]]) -> float:
        """Calculate score for a content type based on pattern matching"""
        total_score = 0.0
        text_lower = text.lower()

        for pattern_info in patterns:
            pattern = pattern_info['pattern']
            weight = pattern_info['weight']

            if re.search(pattern, text, re.IGNORECASE):
                total_score += weight

        # Apply context-based adjustments
        if context:
            total_score = self._apply_context_adjustments(total_score, text, context)

        # Apply length-based adjustments
        total_score = self._apply_length_adjustments(total_score, text)

        return min(total_score, 1.0)  # Cap at 1.0

    def _apply_context_adjustments(self, score: float, text: str, context: Dict[str, Any]) -> float:
        """Apply context-based score adjustments"""
        # Position-based adjustments
        position = context.get('position', 'middle')

        if position == 'beginning':
            # More likely to be title or heading at the beginning
            if len(text) < 100:
                score += 0.1
        elif position == 'end':
            # More likely to be references or conclusion at the end
            if 'reference' in text.lower() or 'conclusion' in text.lower():
                score += 0.1

        # Surrounding content adjustments
        prev_content = context.get('previous_content', '')
        next_content = context.get('next_content', '')

        if prev_content:
            # If previous content was a heading, this is likely body text
            if any(keyword in prev_content.lower() for keyword in ['introduction', 'method', 'result']):
                if len(text) > 50:  # Longer text is more likely to be body
                    score += 0.1 if 'body' in str(score) else 0.0

        return score

    def _apply_length_adjustments(self, score: float, text: str) -> float:
        """Apply length-based score adjustments"""
        length = len(text)

        # Title length adjustments
        if 10 <= length <= 150:
            # Good title length range
            if 'title' in str(score):
                score += 0.1
        elif length > 200:
            # Too long for title
            if 'title' in str(score):
                score -= 0.2

        # Body text length adjustments
        if length > 100:
            # Longer text is more likely to be body content
            if 'body' in str(score):
                score += 0.1

        return score

    def _generate_metadata(self, text: str, content_type: ContentType, context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate metadata for the analyzed content"""
        metadata = {
            'length': len(text),
            'word_count': len(text.split()),
            'has_numbers': bool(re.search(r'\d', text)),
            'has_punctuation': bool(re.search(r'[.!?;:]', text)),
            'starts_with_capital': text[0].isupper() if text else False,
        }

        # Content-type specific metadata
        if content_type == ContentType.SECTION_HEADING:
            # Extract section number if present
            match = re.match(r'^(\d+(?:\.\d+)*)', text)
            if match:
                metadata['section_number'] = match.group(1)
                metadata['section_level'] = len(match.group(1).split('.'))

        elif content_type == ContentType.REFERENCE_ENTRY:
            # Extract reference number or author
            if text.startswith('['):
                match = re.match(r'^\[(\d+)\]', text)
                if match:
                    metadata['reference_number'] = int(match.group(1))
            elif re.match(r'^\d+\.', text):
                match = re.match(r'^(\d+)\.', text)
                if match:
                    metadata['reference_number'] = int(match.group(1))

        elif content_type in [ContentType.FIGURE_CAPTION, ContentType.TABLE_CAPTION]:
            # Extract figure/table number
            pattern = r'(figure|fig\.?|table)\s*(\d+)'
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                metadata['number'] = int(match.group(2))
                metadata['type'] = match.group(1).lower()

        # Academic content analysis
        metadata['academic_keywords'] = self._find_academic_keywords(text)

        return metadata

    def _find_academic_keywords(self, text: str) -> Dict[str, List[str]]:
        """Find academic keywords in the text"""
        found_keywords = {}
        text_lower = text.lower()

        for category, keywords in self.academic_keywords.items():
            found = []
            for keyword in keywords:
                if keyword in text_lower:
                    found.append(keyword)
            if found:
                found_keywords[category] = found

        return found_keywords

    def _suggest_style(self, content_type: ContentType, metadata: Dict[str, Any]) -> str:
        """Suggest appropriate formatting style based on content type and metadata"""
        # Map content types to style names
        style_mapping = {
            ContentType.TITLE: 'title',
            ContentType.SUBTITLE: 'subtitle',
            ContentType.AUTHOR: 'author',
            ContentType.AFFILIATION: 'affiliation',
            ContentType.ABSTRACT_HEADING: 'abstract_heading',
            ContentType.ABSTRACT_BODY: 'abstract_body',
            ContentType.KEYWORDS: 'keywords',
            ContentType.SECTION_HEADING: 'section_heading',
            ContentType.SUBSECTION_HEADING: 'subsection_heading',
            ContentType.SUBSUBSECTION_HEADING: 'subsection_heading',  # Use same as subsection
            ContentType.BODY_TEXT: 'body_text',
            ContentType.FIGURE_CAPTION: 'figure_caption',
            ContentType.TABLE_CAPTION: 'table_caption',
            ContentType.EQUATION: 'equation',
            ContentType.REFERENCE_HEADING: 'reference_heading',
            ContentType.REFERENCE_ENTRY: 'reference_entry',
            ContentType.FOOTNOTE: 'body_text',  # Use body text style for footnotes
            ContentType.QUOTE: 'body_text',     # Use body text style for quotes
            ContentType.LIST_ITEM: 'body_text', # Use body text style for list items
            ContentType.CODE_BLOCK: 'body_text' # Use body text style for code
        }

        base_style = style_mapping.get(content_type, 'body_text')

        # Apply metadata-based adjustments
        if content_type == ContentType.SECTION_HEADING:
            section_level = metadata.get('section_level', 1)
            if section_level > 1:
                base_style = 'subsection_heading'

        return base_style

    def analyze_document_structure(self, content_blocks: List[str]) -> Dict[str, Any]:
        """
        Analyze the overall document structure to improve individual content analysis

        Args:
            content_blocks: List of text blocks from the document

        Returns:
            Dict containing document structure analysis
        """
        structure = {
            'has_title': False,
            'has_abstract': False,
            'has_sections': False,
            'has_references': False,
            'section_count': 0,
            'reference_count': 0,
            'document_type': 'unknown'
        }

        # Analyze each block
        for i, block in enumerate(content_blocks):
            analysis = self.analyze_content(block, {'position': self._get_position(i, len(content_blocks))})

            if analysis.content_type == ContentType.TITLE and i < 3:  # Title should be near the beginning
                structure['has_title'] = True
            elif analysis.content_type == ContentType.ABSTRACT_BODY:
                structure['has_abstract'] = True
            elif analysis.content_type == ContentType.SECTION_HEADING:
                structure['has_sections'] = True
                structure['section_count'] += 1
            elif analysis.content_type == ContentType.REFERENCE_ENTRY:
                structure['has_references'] = True
                structure['reference_count'] += 1

        # Determine document type
        structure['document_type'] = self._determine_document_type(structure)

        return structure

    def _get_position(self, index: int, total: int) -> str:
        """Determine relative position in document"""
        if index < total * 0.2:
            return 'beginning'
        elif index > total * 0.8:
            return 'end'
        else:
            return 'middle'

    def _determine_document_type(self, structure: Dict[str, Any]) -> str:
        """Determine document type based on structure analysis"""
        if structure['has_abstract'] and structure['section_count'] >= 3 and structure['has_references']:
            return 'academic_paper'
        elif structure['has_sections'] and structure['has_references']:
            return 'research_article'
        elif structure['section_count'] >= 2:
            return 'structured_document'
        else:
            return 'simple_document'
