import docx
from typing import Dict, List, Any
import re
from docx.document import Document
from docx.text.paragraph import Paragraph
from docx.table import Table

class DocxParser:
    """
    Service to parse DOCX files and extract structured content
    """
    
    def __init__(self):
        self.heading_patterns = [
            r'^(abstract|introduction|methods?|results?|discussion|conclusion|references?)$',
            r'^(\d+\.?\s*)(.*)',  # Numbered sections
            r'^([A-Z][A-Z\s]+)$',  # ALL CAPS headings
        ]
    
    async def parse_docx(self, file_path: str) -> Dict[str, Any]:
        """
        Parse a DOCX file and extract structured content
        """
        try:
            doc = docx.Document(file_path)
            
            # Extract document structure
            parsed_data = {
                'title': self._extract_title(doc),
                'authors': self._extract_authors(doc),
                'abstract': self._extract_abstract(doc),
                'sections': self._extract_sections(doc),
                'references': self._extract_references(doc),
                'figures': self._extract_figures(doc),
                'tables': self._extract_tables(doc),
                'keywords': self._extract_keywords(doc),
                'metadata': self._extract_metadata(doc)
            }
            
            return parsed_data
            
        except Exception as e:
            raise Exception(f"Error parsing DOCX file: {str(e)}")
    
    def _extract_title(self, doc: Document) -> str:
        """Extract the document title"""
        # Look for title in the first few paragraphs
        for i, paragraph in enumerate(doc.paragraphs[:5]):
            text = paragraph.text.strip()
            if text and len(text) > 10:
                # Check if it's likely a title (not too long, not a heading pattern)
                if len(text) < 200 and not self._is_heading_pattern(text):
                    return text
        return ""
    
    def _extract_authors(self, doc: Document) -> List[Dict[str, Any]]:
        """Extract author information"""
        authors = []
        
        # Look for author patterns in the first few paragraphs
        for paragraph in doc.paragraphs[:10]:
            text = paragraph.text.strip()
            
            # Simple pattern matching for authors
            if self._looks_like_authors(text):
                author_names = self._parse_author_names(text)
                authors.extend(author_names)
        
        return authors
    
    def _extract_abstract(self, doc: Document) -> Dict[str, Any]:
        """Extract abstract content"""
        abstract_sections = []
        in_abstract = False
        current_section = None
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            
            if not text:
                continue
                
            # Check if we're entering abstract section
            if re.search(r'^abstract\s*$', text, re.IGNORECASE):
                in_abstract = True
                continue
            
            # Check if we're leaving abstract section
            if in_abstract and self._is_section_heading(text):
                break
            
            if in_abstract:
                # Check if this is a subsection of abstract (Background, Methods, etc.)
                if self._is_abstract_subsection(text):
                    if current_section:
                        abstract_sections.append(current_section)
                    current_section = {
                        'title': text,
                        'content': ''
                    }
                elif current_section:
                    current_section['content'] += text + '\n'
                else:
                    # No subsection, treat as general abstract
                    if not abstract_sections:
                        abstract_sections.append({
                            'title': 'Abstract',
                            'content': text + '\n'
                        })
                    else:
                        abstract_sections[0]['content'] += text + '\n'
        
        # Add the last section if exists
        if current_section:
            abstract_sections.append(current_section)
        
        return {'sections': abstract_sections}
    
    def _extract_sections(self, doc: Document) -> List[Dict[str, Any]]:
        """Extract main document sections"""
        sections = []
        current_section = None
        skip_until_main_content = True
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            
            if not text:
                continue
            
            # Skip until we find main content (after abstract)
            if skip_until_main_content:
                if re.search(r'^(introduction|methods?|background)', text, re.IGNORECASE):
                    skip_until_main_content = False
                else:
                    continue
            
            # Check if this is a section heading
            if self._is_section_heading(text):
                # Save previous section
                if current_section:
                    sections.append(current_section)
                
                # Start new section
                current_section = {
                    'title': text,
                    'content': '',
                    'subsections': []
                }
            elif current_section:
                # Add content to current section
                current_section['content'] += text + '\n\n'
        
        # Add the last section
        if current_section:
            sections.append(current_section)
        
        return sections
    
    def _extract_references(self, doc: Document) -> List[Dict[str, Any]]:
        """Extract references"""
        references = []
        in_references = False
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            
            if not text:
                continue
            
            # Check if we're in references section
            if re.search(r'^references?\s*$', text, re.IGNORECASE):
                in_references = True
                continue
            
            if in_references:
                # Each paragraph is likely a reference
                if text and len(text) > 20:  # Reasonable reference length
                    references.append({
                        'id': f'ref-{len(references) + 1}',
                        'content': text
                    })
        
        return references
    
    def _extract_figures(self, doc: Document) -> List[Dict[str, Any]]:
        """Extract figure information"""
        figures = []
        
        # Look for figure captions and references
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            
            if re.search(r'^figure\s+\d+', text, re.IGNORECASE):
                figures.append({
                    'id': f'fig-{len(figures) + 1}',
                    'label': text.split('.')[0] if '.' in text else text,
                    'caption': text,
                    'position': 'float'
                })
        
        return figures
    
    def _extract_tables(self, doc: Document) -> List[Dict[str, Any]]:
        """Extract table information"""
        tables = []
        
        # Extract actual tables from document
        for i, table in enumerate(doc.tables):
            table_data = []
            for row in table.rows:
                row_data = [cell.text.strip() for cell in row.cells]
                table_data.append(row_data)
            
            tables.append({
                'id': f'table-{i + 1}',
                'label': f'Table {i + 1}',
                'caption': '',  # Will be enhanced by AI
                'content': table_data
            })
        
        return tables
    
    def _extract_keywords(self, doc: Document) -> List[str]:
        """Extract keywords"""
        keywords = []
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            
            if re.search(r'^keywords?\s*:', text, re.IGNORECASE):
                # Extract keywords after the colon
                keyword_text = re.sub(r'^keywords?\s*:\s*', '', text, flags=re.IGNORECASE)
                keywords = [kw.strip() for kw in keyword_text.split(',')]
                break
        
        return keywords
    
    def _extract_metadata(self, doc: Document) -> Dict[str, Any]:
        """Extract document metadata"""
        return {
            'word_count': len(doc.paragraphs),
            'has_tables': len(doc.tables) > 0,
            'has_images': False,  # TODO: Detect images
        }
    
    def _is_heading_pattern(self, text: str) -> bool:
        """Check if text matches heading patterns"""
        for pattern in self.heading_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False
    
    def _is_section_heading(self, text: str) -> bool:
        """Check if text is a section heading"""
        # Common section headings
        section_patterns = [
            r'^(introduction|background|methods?|methodology|results?|discussion|conclusion|references?)$',
            r'^\d+\.?\s+[A-Z]',  # Numbered sections
            r'^[A-Z][A-Z\s]+$',  # ALL CAPS
        ]
        
        for pattern in section_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False
    
    def _is_abstract_subsection(self, text: str) -> bool:
        """Check if text is an abstract subsection"""
        subsection_patterns = [
            r'^(background|objective|methods?|results?|conclusion)s?\s*:?$'
        ]
        
        for pattern in subsection_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False
    
    def _looks_like_authors(self, text: str) -> bool:
        """Check if text looks like author names"""
        # Simple heuristics for author detection
        if len(text) > 200:  # Too long for authors
            return False
        
        # Look for patterns like "First Last, First Last"
        if re.search(r'[A-Z][a-z]+\s+[A-Z][a-z]+', text):
            return True
        
        return False
    
    def _parse_author_names(self, text: str) -> List[Dict[str, Any]]:
        """Parse author names from text"""
        authors = []
        
        # Split by common separators
        names = re.split(r'[,;]|\sand\s', text)
        
        for name in names:
            name = name.strip()
            if name:
                # Simple name parsing
                parts = name.split()
                if len(parts) >= 2:
                    authors.append({
                        'given_names': ' '.join(parts[:-1]),
                        'surname': parts[-1],
                        'affiliations': []
                    })
        
        return authors
