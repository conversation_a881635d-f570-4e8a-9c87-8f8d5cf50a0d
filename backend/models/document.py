from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

# Pydantic models that mirror the TypeScript JATS interfaces

class JournalId(BaseModel):
    type: str  # 'publisher-id' | 'nlm-ta'
    value: str

class JournalMeta(BaseModel):
    journal_ids: List[JournalId]
    journal_title: str
    abbrev_journal_title: str
    issn_print: Optional[str] = None
    issn_electronic: Optional[str] = None
    publisher: Dict[str, str]

class ArticleId(BaseModel):
    type: str  # 'publisher-id' | 'doi'
    value: str

class ArticleCategory(BaseModel):
    type: str
    subject: str

class ContributorName(BaseModel):
    surname: str
    given_names: str

class Contributor(BaseModel):
    type: str  # 'author'
    name: ContributorName
    orcid: Optional[str] = None
    affiliation_refs: List[str]
    is_corresponding: Optional[bool] = False

class Affiliation(BaseModel):
    id: str
    label: str
    department: Optional[str] = None
    institution: str
    address_line: Optional[str] = None
    country: str
    country_code: Optional[str] = None

class AuthorNote(BaseModel):
    id: str
    content: str

class Correspondence(BaseModel):
    id: str
    content: str
    email: str

class PubDate(BaseModel):
    type: str  # 'epub' | 'ppub'
    day: str
    month: str
    year: str

class HistoryDate(BaseModel):
    type: str  # 'received' | 'accepted'
    day: str
    month: str
    year: str

class License(BaseModel):
    href: str
    content: str

class Permissions(BaseModel):
    copyright_statement: str
    copyright_year: str
    copyright_holder: str
    license: License

class AbstractSection(BaseModel):
    title: str
    content: str

class Abstract(BaseModel):
    sections: List[AbstractSection]

class Keyword(BaseModel):
    value: str

class KeywordGroup(BaseModel):
    type: str
    title: str
    keywords: List[Keyword]

class FundingSource(BaseModel):
    id: str
    name: str
    award_id: str

class FundingGroup(BaseModel):
    funding_sources: List[FundingSource]

class CustomMeta(BaseModel):
    name: str
    value: str

class ArticleMeta(BaseModel):
    article_ids: List[ArticleId]
    article_categories: List[ArticleCategory]
    title_group: Dict[str, str]  # {"article_title": "..."}
    contributors: List[Contributor]
    affiliations: List[Affiliation]
    author_notes: List[AuthorNote]
    correspondences: List[Correspondence]
    pub_dates: List[PubDate]
    volume: str
    issue: str
    first_page: str
    last_page: str
    history: List[HistoryDate]
    permissions: Permissions
    abstract: Abstract
    keyword_groups: List[KeywordGroup]
    funding_groups: List[FundingGroup]
    custom_meta: List[CustomMeta]

class Reference(BaseModel):
    id: str
    content: str

class Figure(BaseModel):
    id: str
    position: str
    type: str
    label: str
    caption: str
    graphic: str

class Table(BaseModel):
    id: str
    position: str
    label: str
    caption: str
    content: str

class Formula(BaseModel):
    id: str
    display: str  # 'inline' | 'block'
    mathml: str
    label: Optional[str] = None

class ListItem(BaseModel):
    content: str

class DocumentList(BaseModel):
    id: str
    type: str  # 'roman-upper' | 'bullet' | 'ordered'
    items: List[str]

class Section(BaseModel):
    id: Optional[str] = None
    type: Optional[str] = None
    title: str
    content: str
    subsections: Optional[List['Section']] = None
    figures: Optional[List[Figure]] = None
    tables: Optional[List[Table]] = None
    formulas: Optional[List[Formula]] = None
    lists: Optional[List[DocumentList]] = None
    references: Optional[List[str]] = None  # Reference IDs

class Body(BaseModel):
    sections: List[Section]

class Back(BaseModel):
    references: List[Reference]

class JATSDocument(BaseModel):
    article_type: str
    dtd_version: str
    language: str
    front: Dict[str, Any]  # Contains journal_meta and article_meta
    body: Body
    back: Back

class DocumentResponse(BaseModel):
    success: bool
    message: str
    jats_document: Optional[JATSDocument] = None
    filename: Optional[str] = None
    error: Optional[str] = None

# Update forward references
Section.model_rebuild()
