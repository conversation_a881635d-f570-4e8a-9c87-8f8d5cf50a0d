#!/bin/bash

echo "🚀 Starting AME Star Frontend Development Server"
echo "=============================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully!"

if [ ! -f ".env" ]; then
    echo "📝 Creating environment file..."
    cp .env.example .env
fi

echo "🚀 Starting development server..."
echo ""
echo "📖 The application will be available at:"
echo "   Frontend: http://localhost:3000"
echo ""
echo "💡 Features to test:"
echo "   - Click 'Try Demo Document' to load sample content"
echo "   - Test rich text editing in each field"
echo "   - Try the toolbar formatting options"
echo "   - Check the real-time preview panel"
echo ""

npm run dev
