<!doctype html>
<html lang="zh">
    <head>
        <meta charset="UTF-8" />
        <title>批量 XML 训练 & JATS 生成示例（带训练进度）</title>
        <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.11.0/dist/tf.min.js"></script>
    </head>
    <body>
        <h1>批量 XML 文件训练 & JATS 生成示例（带训练进度）</h1>

        <input type="file" id="fileInput" multiple accept=".xml" />
        <br /><br />
        <button id="train">训练模型</button>
        <button id="predict">预测 & 生成 XML</button>
        <hr>
        <div style="margin: 20px 0;">
            <h3>测试分类</h3>
            <textarea id="testInput" rows="4" cols="50" placeholder="在此输入要测试的文本..." style="width: 100%; margin-bottom: 10px;"></textarea>
            <button id="test">测试分类</button>
            <div id="testResult" style="margin-top: 10px; padding: 10px; background-color: #f5f5f5;"></div>
        </div>
        <hr>
        <pre id="output"></pre>
        <div id="progress"></div>

        <script>
            (async () => {
                const vocabulary = [
                    "大学",
                    "学院",
                    "医院",
                    "研究所",
                    "实验室",
                    "中心",
                    "Department",
                    "Institute",
                    "Lab",
                    "Medical",
                    "Learning",
                    "Deep",
                    "Cancer",
                    "Diagnosis",
                    "Novel",
                    "Approach",
                    "Xiaoming",
                    "Fang",
                    "Zhang",
                    "Liu",
                    "Carl",
                    "Maki",
                    "Ruichen",
                    "Yu",
                    "Ping",
                    "Dan",
                ];

                function textToVector(text) {
                    const vec = new Array(vocabulary.length).fill(0);
                    const words = text
                        .split(/\s|,|，|\.|#|\^|1|2|3|4|5|6|7|8|9|0/)
                        .map((w) => w.trim())
                        .filter(Boolean);
                    for (const w of words) {
                        const idx = vocabulary.indexOf(w);
                        if (idx !== -1) vec[idx] = 1;
                    }
                    return vec;
                }

                const model = tf.sequential();
                model.add(
                    tf.layers.dense({
                        inputShape: [vocabulary.length],
                        units: 32,
                        activation: "relu",
                    })
                );
                model.add(tf.layers.dense({ units: 16, activation: "relu" }));
                model.add(tf.layers.dense({ units: 4, activation: "softmax" }));
                model.compile({
                    optimizer: "adam",
                    loss: "categoricalCrossentropy",
                    metrics: ["accuracy"],
                });

                let trainTexts = [];
                let trainLabels = [];

                // -----------------------------
                // 上传 XML 文件并解析
                // -----------------------------
                document
                    .getElementById("fileInput")
                    .addEventListener("change", async (event) => {
                        const files = event.target.files;
                        trainTexts = [];
                        trainLabels = [];
                        for (const file of files) {
                            const text = await file.text();
                            const parser = new DOMParser();
                            const xmlDoc = parser.parseFromString(
                                text,
                                "application/xml"
                            );

                            const titleNodes =
                                xmlDoc.getElementsByTagName("article-title");
                            for (const node of titleNodes) {
                                trainTexts.push(node.textContent.trim());
                                trainLabels.push([1, 0, 0, 0]);
                            }

                            const authorNodes = xmlDoc.querySelectorAll(
                                "contrib[contrib-type='author']"
                            );
                            let authorNodesStr = "";
                            authorNodes.forEach((a) => {
                                let surname = a.querySelector("surname")
                                    ? a.querySelector("surname").textContent
                                    : "";
                                let givenNames = a.querySelector("given-names")
                                    ? a.querySelector("given-names").textContent
                                    : "";
                                let superscripts = Array.from(a.querySelectorAll("sup"))
                                    .map((n) => n.textContent)
                                    .join(",");
                                if (surname || givenNames) {
                                    authorNodesStr += `${givenNames} ${surname}${superscripts}, `;
                                }
                            });
                            authorNodesStr = authorNodesStr.trim();
                            console.log(authorNodesStr)
                            trainTexts.push(authorNodesStr.trim());
                            trainLabels.push([0, 1, 0, 0]);
                            //   for (const node of authorNodes) {
                            //     trainTexts.push(node.textContent.trim());
                            //     trainLabels.push([0,1,0,0]);
                            //   }

                            const affNodes = xmlDoc.getElementsByTagName("aff");
                            for (const node of affNodes) {
                                trainTexts.push(node.textContent.trim());
                                trainLabels.push([0, 0, 1, 0]);
                            }

                            const pNodes = xmlDoc.getElementsByTagName("p");
                            for (const node of pNodes) {
                                const txt = node.textContent.trim();
                                if (txt) {
                                    trainTexts.push(txt);
                                    trainLabels.push([0, 0, 0, 1]);
                                }
                            }
                        }

                        document.getElementById("progress").textContent =
                            `已解析 ${trainTexts.length} 条训练样本`;
                    });

                // -----------------------------
                // 训练模型（带进度输出）
                // -----------------------------
                document.getElementById("train").onclick = async () => {
                    if (trainTexts.length === 0) {
                        alert("请先上传 XML 文件！");
                        return;
                    }

                    const xs = tf.tensor2d(trainTexts.map(textToVector));
                    const ys = tf.tensor2d(trainLabels);

                    await model.fit(xs, ys, {
                        epochs: 100,
                        shuffle: true,
                        callbacks: {
                            onEpochEnd: (epoch, logs) => {
                                document.getElementById(
                                    "progress"
                                ).textContent =
                                    `训练中: 第 ${epoch + 1} 轮, loss=${logs.loss.toFixed(4)}, accuracy=${(logs.acc || logs.accuracy).toFixed(4)}`;
                            },
                        },
                    });

                    await model.save("indexeddb://paragraph-model");
                    alert("训练完成并保存到 IndexedDB！");
                    document.getElementById("progress").textContent =
                        "训练完成";
                };

                // -----------------------------
                // 测试单个文本分类
                // -----------------------------
                document.getElementById("test").onclick = async () => {
                    const testText = document.getElementById("testInput").value.trim();
                    if (!testText) {
                        alert("请输入要测试的文本！");
                        return;
                    }

                    let loadedModel;
                    try {
                        loadedModel = await tf.loadLayersModel("indexeddb://paragraph-model");
                    } catch (e) {
                        console.warn("IndexedDB 没有模型，使用当前模型");
                        loadedModel = model;
                    }

                    const inputVec = tf.tensor2d([textToVector(testText)]);
                    const pred = loadedModel.predict(inputVec);
                    const probs = pred.dataSync();
                    const idx = pred.argMax(1).dataSync()[0];
                    const types = ["标题", "作者", "机构", "正文"];
                    const type = types[idx];

                    // 格式化概率输出
                    let resultHTML = `<strong>分类结果：${type}</strong><br><br>各类别概率：<br>`;
                    types.forEach((t, i) => {
                        const probability = (probs[i] * 100).toFixed(2);
                        resultHTML += `${t}: ${probability}%<br>`;
                    });

                    // 如果是作者行，添加正则匹配结果
                    if (isAuthorLineRegex(testText)) {
                        resultHTML += `<br><em>注：通过作者行正则表达式匹配确认，这很可能是一个作者行。</em>`;
                    }

                    document.getElementById("testResult").innerHTML = resultHTML;
                };

                // -----------------------------
                // 作者行正则
                // -----------------------------
                function isAuthorLineRegex(line) {
                    const parts = line
                        .split(/[,、;]+/)
                        .map((p) => p.trim())
                        .filter(Boolean);
                    const authorRegex =
                        /^([A-Z][a-z]+(\s[A-Z]\.)?(\s[A-Z][a-z]+)?|[\u4e00-\u9fa5]{2,4})(\d+|\^|#|\*|,?\d+)?$/;
                    let matchCount = 0;
                    for (const part of parts) {
                        if (authorRegex.test(part)) matchCount++;
                    }
                    return matchCount / parts.length >= 0.7;
                }

                // -----------------------------
                // 批量预测 & XML生成
                // -----------------------------
                document.getElementById("predict").onclick = async () => {
                    let loadedModel;
                    try {
                        loadedModel = await tf.loadLayersModel(
                            "indexeddb://paragraph-model"
                        );
                    } catch (e) {
                        console.warn("IndexedDB 没有模型，使用当前模型");
                        loadedModel = model;
                    }

                    const xmlLines = [];
                    for (const line of trainTexts) {
                        let type = "other";
                        if (isAuthorLineRegex(line)) type = "author";
                        else {
                            const inputVec = tf.tensor2d([textToVector(line)]);
                            const pred = loadedModel.predict(inputVec);
                            const idx = pred.argMax(1).dataSync()[0];
                            type = ["title", "author", "affiliation", "other"][
                                idx
                            ];
                        }

                        if (type === "title") {
                            xmlLines.push(
                                `<article-title>${line}</article-title>`
                            );
                        } else if (type === "author") {
                            xmlLines.push(
                                `<contrib contrib-type="author">${line}</contrib>`
                            );
                        } else if (type === "affiliation") {
                            xmlLines.push(`<aff id="aff1">${line}</aff>`);
                        } else {
                            xmlLines.push(`<p>${line}</p>`);
                        }
                    }

                    document.getElementById("output").textContent =
                        xmlLines.join("\n");
                };
            })();
        </script>
    </body>
</html>
