import { useCallback } from 'react';
import { EditorState } from 'lexical';
import { useAppDispatch, useAppSelector } from './redux';
import {
  loadDocument,
  updateEditorState,
  updateJATSField,
  setActiveEditor,
  setPreviewMode,
  setLoading,
  setError,
  setSplitViewRatio,
  setArticleState,
  addReference,
  updateReference,
  removeReference,
} from '../store/documentSlice';
import { JATSDocument } from '../types/jats';
import { EditableField } from '../types/editor';

export const useDocument = () => {
  const dispatch = useAppDispatch();
  const documentState = useAppSelector((state) => state.document);

  // Document actions
  const loadDoc = useCallback((jatsDocument: JATSDocument, fileName?: string) => {
    dispatch(loadDocument({ jatsDocument, fileName }));
  }, [dispatch]);

  const updateEditor = useCallback((fieldId: string, editorState: EditorState) => {
    dispatch(updateEditorState({ fieldId, editorState }));
  }, [dispatch]);

  const updateJATSFieldValue = useCallback((path: string, value: any) => {
    dispatch(updateJATSField({ path, value }));
  }, [dispatch]);

  const getJatsDocumentValue = useCallback(() => {
    return documentState.jatsDocument;
  }, [documentState.jatsDocument]);

  // ArticleState actions
  const setArticle = useCallback((article: import('../types/articleState').ArticleState) => {
    dispatch(setArticleState({ article }));
  }, [dispatch]);
  const addRef = useCallback((ref: import('../types/articleState').ReferenceEntry) => {
    dispatch(addReference({ ref }));
  }, [dispatch]);
  const updateRef = useCallback((id: string, patch: Partial<import('../types/articleState').ReferenceEntry>) => {
    dispatch(updateReference({ id, patch }));
  }, [dispatch]);
  const removeRef = useCallback((id: string) => {
    dispatch(removeReference({ id }));
  }, [dispatch]);

  const setActive = useCallback((fieldId: string) => {
    dispatch(setActiveEditor({ fieldId }));
  }, [dispatch]);

  const setPreview = useCallback((mode: 'xml' | 'html' | 'pdf') => {
    dispatch(setPreviewMode({ mode }));
  }, [dispatch]);

  const setLoadingState = useCallback((isLoading: boolean) => {
    dispatch(setLoading({ isLoading }));
  }, [dispatch]);

  const setErrorState = useCallback((error?: string) => {
    dispatch(setError({ error }));
  }, [dispatch]);

  const setSplitRatio = useCallback((ratio: number) => {
    dispatch(setSplitViewRatio({ ratio }));
  }, [dispatch]);

  // Selectors
  const getEditorState = useCallback((fieldId: string) => {
    const { editorStates } = documentState;

    if (fieldId === 'unifiedDocument') {
      return editorStates.unifiedDocument;
    } else if (fieldId === 'articleTitle') {
      return editorStates.articleTitle;
    } else if (fieldId === 'keywords') {
      return editorStates.keywords;
    } else if (fieldId.startsWith('abstractSection:')) {
      const sectionId = fieldId.replace('abstractSection:', '');
      return editorStates.abstractSections[sectionId];
    } else if (fieldId.startsWith('bodySection:')) {
      const sectionId = fieldId.replace('bodySection:', '');
      return editorStates.bodySections[sectionId];
    } else if (fieldId.startsWith('reference:')) {
      const refId = fieldId.replace('reference:', '');
      return editorStates.references[refId];
    } else if (fieldId.startsWith('figureCaption:')) {
      const figId = fieldId.replace('figureCaption:', '');
      return editorStates.figureCaptions[figId];
    } else if (fieldId.startsWith('tableCaption:')) {
      const tableId = fieldId.replace('tableCaption:', '');
      return editorStates.tableCaptions[tableId];
    } else if (fieldId.startsWith('authorNote:')) {
      const noteId = fieldId.replace('authorNote:', '');
      return editorStates.authorNotes[noteId];
    } else if (fieldId.startsWith('correspondence:')) {
      const corrId = fieldId.replace('correspondence:', '');
      return editorStates.correspondences[corrId];
    }
    
    return null;
  }, [documentState.editorStates]);

  const getJATSFieldValue = useCallback((path: string) => {
    const pathParts = path.split('.');
    let current: any = documentState.jatsDocument;
    
    for (const part of pathParts) {
      if (current && typeof current === 'object') {
        current = current[part];
      } else {
        return undefined;
      }
    }
    
    return current;
  }, [documentState.jatsDocument]);

  // Utility functions
  const isFieldDirty = useCallback((fieldId: string) => {
    const editorState = getEditorState(fieldId);
    return editorState?.lastModified ?
      editorState.lastModified > (documentState.metadata.uploadedAt || 0) :
      false;
  }, [getEditorState, documentState.metadata.uploadedAt]);

  const getFieldDisplayName = useCallback((fieldId: EditableField): string => {
    if (fieldId === 'unifiedDocument') return 'Document Editor';
    if (fieldId === 'articleTitle') return 'Article Title';
    if (fieldId === 'keywords') return 'Keywords';
    if (fieldId.startsWith('abstractSection:')) {
      const sectionId = fieldId.replace('abstractSection:', '');
      // Try to get the actual section title from JATS document
      const sections = documentState.jatsDocument.front.articleMeta.abstract.sections;
      const sectionIndex = parseInt(sectionId.replace('section-', ''));
      return sections[sectionIndex]?.title || `Abstract Section ${sectionIndex + 1}`;
    }
    if (fieldId.startsWith('bodySection:')) {
      const sectionId = fieldId.replace('bodySection:', '');
      // Try to find the section title
      const findSectionTitle = (sections: any[], id: string): string => {
        for (const section of sections) {
          if (section.id === id) return section.title;
          if (section.subsections) {
            const found = findSectionTitle(section.subsections, id);
            if (found) return found;
          }
        }
        return '';
      };
      const title = findSectionTitle(documentState.jatsDocument.body.sections, sectionId);
      return title || `Section ${sectionId}`;
    }
    if (fieldId.startsWith('reference:')) {
      const refId = fieldId.replace('reference:', '');
      return `Reference ${refId}`;
    }
    if (fieldId.startsWith('figureCaption:')) {
      const figId = fieldId.replace('figureCaption:', '');
      return `Figure ${figId} Caption`;
    }
    if (fieldId.startsWith('tableCaption:')) {
      const tableId = fieldId.replace('tableCaption:', '');
      return `Table ${tableId} Caption`;
    }
    if (fieldId.startsWith('authorNote:')) {
      const noteId = fieldId.replace('authorNote:', '');
      return `Author Note ${noteId}`;
    }
    if (fieldId.startsWith('correspondence:')) {
      const corrId = fieldId.replace('correspondence:', '');
      return `Correspondence ${corrId}`;
    }
    
    return fieldId;
  }, [documentState.jatsDocument]);


  return {
    // State
    documentState,
    jatsDocument: documentState.jatsDocument,
    metadata: documentState.metadata,
    ui: documentState.ui,
    
    // Actions
    loadDoc,
    updateEditor,
    updateJATSFieldValue,
    setActive,
    setPreview,
    setLoadingState,
    setErrorState,
    setSplitRatio,
    getJatsDocumentValue,

    // ArticleState actions
    setArticle,
    addRef,
    updateRef,
    removeRef,

    // Selectors
    getEditorState,
    getJATSFieldValue,
    isFieldDirty,
    getFieldDisplayName,
  };
};
