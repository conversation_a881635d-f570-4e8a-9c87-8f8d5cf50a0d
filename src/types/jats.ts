// JATS XML Schema TypeScript Interfaces
// These interfaces mirror the JATS XML structure exactly for 1:1 mapping

export interface JournalId {
  type: 'publisher-id' | 'nlm-ta';
  value: string;
}

export interface JournalMeta {
  journalIds: JournalId[];
  journalTitle: string;
  abbrevJournalTitle: string;
  issnPrint?: string;
  issnElectronic?: string;
  publisher: {
    name: string;
  };
}

export interface ArticleId {
  type: 'publisher-id' | 'doi';
  value: string;
}

export interface ArticleCategory {
  type: string;
  subject: string;
}

export interface ContributorName {
  surname: string;
  givenNames: string;
}

export interface Contributor {
  type: 'author';
  name: ContributorName;
  orcid?: string;
  affiliationRefs: string[];
  isCorresponding?: boolean;
}

export interface Affiliation {
  id: string;
  label: string;
  department?: string;
  institution: string;
  addressLine?: string;
  country: string;
  countryCode?: string;
}

export interface AuthorNote {
  id: string;
  content: string;
}

export interface Correspondence {
  id: string;
  content: string;
  email: string;
}

export interface PubDate {
  type: 'epub' | 'ppub';
  day: string;
  month: string;
  year: string;
}

export interface HistoryDate {
  type: 'received' | 'accepted';
  day: string;
  month: string;
  year: string;
}

export interface License {
  href: string;
  content: string;
}

export interface Permissions {
  copyrightStatement: string;
  copyrightYear: string;
  copyrightHolder: string;
  license: License;
}

export interface AbstractSection {
  title: string;
  content: string;
}

export interface Abstract {
  sections: AbstractSection[];
}

export interface Keyword {
  value: string;
}

export interface KeywordGroup {
  type: string;
  title: string;
  keywords: Keyword[];
}

export interface FundingSource {
  id: string;
  name: string;
  awardId: string;
}

export interface FundingGroup {
  fundingSources: FundingSource[];
}

export interface CustomMeta {
  name: string;
  value: string;
}

export interface ArticleMeta {
  articleIds: ArticleId[];
  articleCategories: ArticleCategory[];
  titleGroup: {
    articleTitle: string;
  };
  contributors: Contributor[];
  affiliations: Affiliation[];
  authorNotes: AuthorNote[];
  correspondences: Correspondence[];
  pubDates: PubDate[];
  volume: string;
  issue: string;
  firstPage: string;
  lastPage: string;
  history: HistoryDate[];
  permissions: Permissions;
  abstract: Abstract;
  keywordGroups: KeywordGroup[];
  fundingGroups: FundingGroup[];
  customMeta: CustomMeta[];
}

export interface ReferencePerson { surname: string; givenNames: string }
export interface ReferencePubId { type: string; value: string }
export interface MixedCitation {
  publicationType?: string; // e.g., 'journal'
  authors?: ReferencePerson[]; // person-group type="author"
  articleTitle?: string;
  source?: string; // journal/book/source
  year?: string;
  volume?: string;
  fpage?: string;
  lpage?: string;
  pubIds?: ReferencePubId[]; // doi/pmid/pmcid
}

export interface Reference {
  id: string; // r1
  label?: string; // '1'
  mixedCitation?: MixedCitation; // structured
  raw?: string; // fallback plain text
}

export interface Figure {
  id: string;
  position: string;
  type: string;
  label: string;
  caption: string;
  graphic: string;
}

export interface Table {
  id: string;
  position: string;
  label: string;
  caption: string;
  content: string;
}

export interface Formula {
  id: string;
  display: 'inline' | 'block';
  mathml: string;
  label?: string;
}

export interface DocumentList {
  id: string;
  type: 'roman-upper' | 'bullet' | 'ordered';
  items: string[];
}

export interface Section {
  id?: string;
  type?: string;
  title: string;
  content: string;
  subsections?: Section[];
  figures?: Figure[];
  tables?: Table[];
  formulas?: Formula[];
  lists?: DocumentList[];
  references?: string[]; // Reference IDs
}

export interface Body {
  sections: Section[];
}

export interface Back {
  references: Reference[];
}

export interface JATSDocument {
  articleType: string;
  dtdVersion: string;
  language: string;
  front: {
    journalMeta: JournalMeta;
    articleMeta: ArticleMeta;
  };
  body: Body;
  back: Back;
}
