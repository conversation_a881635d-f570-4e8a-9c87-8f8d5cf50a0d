// ArticleState: editor-oriented semantic model that can round-trip with JATS

export type InlineMark = {
  text: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  strike?: boolean;
  code?: boolean;
  subscript?: boolean;
  superscript?: boolean;
  color?: string;
  backgroundColor?: string;
  fontFamily?: string;
  fontSize?: string;
};

export type InlineLink = {
  type: 'link';
  href: string;
  children: Inline[];
};

export type InlineImage = {
  type: 'image';
  src: string;
  alt?: string;
  width?: number | 'inherit';
};

export type InlineEquation = {
  type: 'equation';
  latex: string;
  display?: 'inline' | 'block';
};

export type Inline = InlineMark | InlineLink | InlineImage | InlineEquation;

export type ParagraphBlock = { type: 'paragraph'; id: string; inlines: Inline[] };
export type HeadingBlock = { type: 'heading'; id: string; level: 1|2|3|4|5|6; inlines: Inline[] };
export type ListBlock = { type: 'list'; id: string; ordered: boolean; items: Inline[][] };
export type HrBlock = { type: 'hr'; id: string };

export type TableCell = { inlines: Inline[] };
export type TableRow = TableCell[];
export type TableBlock = { type: 'table'; id: string; rows: TableRow[]; caption?: Inline[] };

export type FigureBlock = { type: 'figure'; id: string; src: string; alt?: string; caption?: Inline[] };
export type EquationBlock = { type: 'equation'; id: string; latex: string; display: 'inline' | 'block'; label?: string };

export type Block = ParagraphBlock | HeadingBlock | ListBlock | HrBlock | TableBlock | FigureBlock | EquationBlock;

export type PersonName = { surname: string; givenNames: string };
export type ReferenceEntry = {
  id: string; // r1, r2, ...
  label?: string; // '1'
  authors: PersonName[];
  articleTitle?: string;
  source?: string; // journal/book/source
  year?: string;
  volume?: string;
  fpage?: string;
  lpage?: string;
  doi?: string;
  pmid?: string;
  raw?: string; // raw mixed-citation xml or plain text (fallback)
};

export type ArticleState = {
  meta: {
    title?: string;
    language?: string;
  };
  blocks: Block[];
  references: ReferenceEntry[];
};

