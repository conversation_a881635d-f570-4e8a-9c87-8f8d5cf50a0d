// Editor-specific types for Lexical integration
import { EditorState } from 'lexical';
import { JATSDocument } from './jats';
import { ArticleState } from './articleState';

export interface EditorFieldState {
  editorState: EditorState | null;
  isEditing: boolean;
  // store timestamps (ms since epoch) to keep Redux state serializable
  lastModified: number;
}

export interface DocumentState {
  // Core JATS document structure
  jatsDocument: JATSDocument;

  // Editor-oriented normalized state
  articleState?: ArticleState;

  // One-shot HTML to import into Lexical editor (client-side docx parsing)
  pendingHTML?: string;

  // Editor states for each editable field
  editorStates: {
    // Unified document editor
    unifiedDocument: EditorFieldState;

    // Article metadata editors
    articleTitle: EditorFieldState;

    // Abstract section editors
    abstractSections: Record<string, EditorFieldState>; // keyed by section title

    // Body section editors (up to 5 levels of nesting)
    bodySections: Record<string, EditorFieldState>; // keyed by section ID

    // Reference editors
    references: Record<string, EditorFieldState>; // keyed by reference ID

    // Figure caption editors
    figureCaptions: Record<string, EditorFieldState>; // keyed by figure ID

    // Table caption editors
    tableCaptions: Record<string, EditorFieldState>; // keyed by table ID

    // Keywords editor
    keywords: EditorFieldState;

    // Author notes editor
    authorNotes: Record<string, EditorFieldState>; // keyed by note ID

    // Correspondence editor
    correspondences: Record<string, EditorFieldState>; // keyed by correspondence ID
  };

  // Document metadata
  metadata: {
    fileName?: string;
    // timestamps (ms since epoch) for serializable Redux state
    uploadedAt?: number;
    lastSaved?: number;
    isDirty: boolean;
    version: number;
  };

  // UI state
  ui: {
    activeEditor?: string; // ID of currently active editor
    previewMode: 'xml' | 'html' | 'pdf';
    isLoading: boolean;
    error?: string;
    splitViewRatio: number; // 0-1, percentage for left panel
  };
}

export interface AppState {
  document: DocumentState;
  // Future: could add multiple documents, user preferences, etc.
}

// Action types for Redux
export interface UpdateEditorStateAction {
  type: 'UPDATE_EDITOR_STATE';
  payload: {
    fieldId: string;
    editorState: EditorState;
  };
}

export interface UpdateJATSFieldAction {
  type: 'UPDATE_JATS_FIELD';
  payload: {
    path: string; // dot notation path like 'front.articleMeta.titleGroup.articleTitle'
    value: any;
  };
}

export interface SetActiveEditorAction {
  type: 'SET_ACTIVE_EDITOR';
  payload: {
    fieldId: string;
  };
}

export interface SetPreviewModeAction {
  type: 'SET_PREVIEW_MODE';
  payload: {
    mode: 'xml' | 'html' | 'pdf';
  };
}

export interface LoadDocumentAction {
  type: 'LOAD_DOCUMENT';
  payload: {
    jatsDocument: JATSDocument;
    fileName?: string;
  };
}

export interface SetLoadingAction {
  type: 'SET_LOADING';
  payload: {
    isLoading: boolean;
  };
}

export interface SetErrorAction {
  type: 'SET_ERROR';
  payload: {
    error?: string;
  };
}

export type DocumentAction = 
  | UpdateEditorStateAction
  | UpdateJATSFieldAction
  | SetActiveEditorAction
  | SetPreviewModeAction
  | LoadDocumentAction
  | SetLoadingAction
  | SetErrorAction;

// Utility types for editor field mapping
export type EditableField =
  | 'unifiedDocument'
  | 'articleTitle'
  | `abstractSection:${string}`
  | `bodySection:${string}`
  | `reference:${string}`
  | `figureCaption:${string}`
  | `tableCaption:${string}`
  | 'keywords'
  | `authorNote:${string}`
  | `correspondence:${string}`;

export interface FieldMapping {
  fieldId: EditableField;
  jatsPath: string; // dot notation path in JATS document
  displayName: string;
  fieldType: 'text' | 'rich-text' | 'list';
}
