import React from 'react';
import { Provider } from 'react-redux';
import { store } from './store';
import { useDocument } from './hooks/useDocument';
import Header from './components/layout/Header';
import SplitView from './components/layout/SplitView';
import EditorPanel from './components/editor/EditorPanel';
import PreviewPanel from './components/preview/PreviewPanel';
import DocumentUpload from './components/upload/DocumentUpload';

function AppContent() {
  const { jatsDocument } = useDocument();

  const hasDocument = jatsDocument.front.articleMeta.titleGroup.articleTitle;

  return (
    <div className="app h-screen flex flex-col bg-gray-100">
      <Header />
      
      <main className="flex-1 overflow-hidden">
        {hasDocument ? (
          <SplitView
            leftPanel={<EditorPanel />}
            rightPanel={<PreviewPanel />}
          />
        ) : (
          <DocumentUpload />
        )}
      </main>
    </div>
  );
}

function App() {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
}

export default App;
