import { configureStore } from '@reduxjs/toolkit';
import documentReducer from './documentSlice';

export const store = configureStore({
  reducer: {
    document: documentReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ['document/updateEditorState'],
        // Ignore these field paths in all actions
        ignoredActionsPaths: ['payload.editorState'],
        // Ignore these paths in the state
        ignoredPaths: ['document.editorStates'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
