import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { EditorState } from 'lexical';
import { DocumentState, EditorFieldState } from '../types/editor';
import { JATSDocument } from '../types/jats';
import { ArticleState, ReferenceEntry } from '../types/articleState';

// Initial state factory
const createInitialEditorFieldState = (): EditorFieldState => ({
  editorState: null,
  isEditing: false,
  lastModified: Date.now(),
});

const createInitialJATSDocument = (): JATSDocument => ({
  articleType: 'research-article',
  dtdVersion: '1.2',
  language: 'en',
  front: {
    journalMeta: {
      journalIds: [],
      journalTitle: '',
      abbrevJournalTitle: '',
      publisher: { name: '' },
    },
    articleMeta: {
      articleIds: [],
      articleCategories: [],
      titleGroup: { articleTitle: '' },
      contributors: [],
      affiliations: [],
      authorNotes: [],
      correspondences: [],
      pubDates: [],
      volume: '',
      issue: '',
      firstPage: '',
      lastPage: '',
      history: [],
      permissions: {
        copyrightStatement: '',
        copyrightYear: '',
        copyrightHolder: '',
        license: { href: '', content: '' },
      },
      abstract: { sections: [] },
      keywordGroups: [],
      fundingGroups: [],
      customMeta: [],
    },
  },
  body: { sections: [] },
  back: { references: [] },
});

const initialState: DocumentState = {
  jatsDocument: createInitialJATSDocument(),
  articleState: undefined,
  pendingHTML: undefined,
  editorStates: {
    unifiedDocument: createInitialEditorFieldState(),
    articleTitle: createInitialEditorFieldState(),
    abstractSections: {},
    bodySections: {},
    references: {},
    figureCaptions: {},
    tableCaptions: {},
    keywords: createInitialEditorFieldState(),
    authorNotes: {},
    correspondences: {},
  },
  metadata: {
    isDirty: false,
    version: 1,
  },
  ui: {
    previewMode: 'xml',
    isLoading: false,
    splitViewRatio: 0.5,
  },
};

const documentSlice = createSlice({
  name: 'document',
  initialState,
  reducers: {
    loadDocument: (state, action: PayloadAction<{ jatsDocument: JATSDocument; fileName?: string }>) => {
      state.jatsDocument = action.payload.jatsDocument;
      state.metadata.fileName = action.payload.fileName;
      state.metadata.uploadedAt = Date.now();
      state.metadata.isDirty = false;
      state.metadata.version = 1;

      // Reset any pending HTML import on new load
      state.pendingHTML = undefined;

      // Initialize editor states for all editable fields
      state.editorStates = {
        unifiedDocument: createInitialEditorFieldState(),
        articleTitle: createInitialEditorFieldState(),
        abstractSections: {},
        bodySections: {},
        references: {},
        figureCaptions: {},
        tableCaptions: {},
        keywords: createInitialEditorFieldState(),
        authorNotes: {},
        correspondences: {},
      };

      // Create editor states for abstract sections
      action.payload.jatsDocument.front.articleMeta.abstract.sections.forEach((section, index) => {
        state.editorStates.abstractSections[`section-${index}`] = createInitialEditorFieldState();
      });

      // Create editor states for body sections
      const createBodySectionStates = (sections: any[], parentId = '') => {
        sections.forEach((section, index) => {
          const sectionId = section.id || `${parentId}section-${index}`;
          state.editorStates.bodySections[sectionId] = createInitialEditorFieldState();

          if (section.subsections) {
            createBodySectionStates(section.subsections, `${sectionId}-`);
          }
        });
      };
      createBodySectionStates(action.payload.jatsDocument.body.sections);

      // Create editor states for references
      action.payload.jatsDocument.back.references.forEach((ref, index) => {
        const refId = ref.id || `ref-${index}`;
        state.editorStates.references[refId] = createInitialEditorFieldState();
      });
    },

    updateEditorState: (state, action: PayloadAction<{ fieldId: string; editorState: EditorState }>) => {
      const { fieldId, editorState } = action.payload;

      // Parse field ID to determine which editor state to update
      if (fieldId === 'unifiedDocument') {
        state.editorStates.unifiedDocument.editorState = editorState;
        state.editorStates.unifiedDocument.lastModified = Date.now();
      } else if (fieldId === 'articleTitle') {
        state.editorStates.articleTitle.editorState = editorState;
        state.editorStates.articleTitle.lastModified = new Date().getTime();
      } else if (fieldId === 'keywords') {
        state.editorStates.keywords.editorState = editorState;
        state.editorStates.keywords.lastModified = Date.now();
      } else if (fieldId.startsWith('abstractSection:')) {
        const sectionId = fieldId.replace('abstractSection:', '');
        if (!state.editorStates.abstractSections[sectionId]) {
          state.editorStates.abstractSections[sectionId] = createInitialEditorFieldState();
        }
        state.editorStates.abstractSections[sectionId].editorState = editorState;
        state.editorStates.abstractSections[sectionId].lastModified = Date.now();
      } else if (fieldId.startsWith('bodySection:')) {
        const sectionId = fieldId.replace('bodySection:', '');
        if (!state.editorStates.bodySections[sectionId]) {
          state.editorStates.bodySections[sectionId] = createInitialEditorFieldState();
        }
        state.editorStates.bodySections[sectionId].editorState = editorState;
        state.editorStates.bodySections[sectionId].lastModified = Date.now();
      } else if (fieldId.startsWith('reference:')) {
        const refId = fieldId.replace('reference:', '');
        if (!state.editorStates.references[refId]) {
          state.editorStates.references[refId] = createInitialEditorFieldState();
        }
        state.editorStates.references[refId].editorState = editorState;
        state.editorStates.references[refId].lastModified = Date.now();
      }

      state.metadata.isDirty = true;
      state.metadata.lastSaved = Date.now();
    },

    updateJATSField: (state, action: PayloadAction<{ path: string; value: any }>) => {
      const { path, value } = action.payload;

      // Use dot notation to update nested fields
      const pathParts = path.split('.');
      let current: any = state.jatsDocument;

      for (let i = 0; i < pathParts.length - 1; i++) {
        current = current[pathParts[i]];
      }

      current[pathParts[pathParts.length - 1]] = value;
      state.metadata.isDirty = true;
      state.metadata.version += 1;
    },

    setActiveEditor: (state, action: PayloadAction<{ fieldId: string }>) => {
      state.ui.activeEditor = action.payload.fieldId;
    },

    setPreviewMode: (state, action: PayloadAction<{ mode: 'xml' | 'html' | 'pdf' }>) => {
      state.ui.previewMode = action.payload.mode;
    },

    setLoading: (state, action: PayloadAction<{ isLoading: boolean }>) => {
      state.ui.isLoading = action.payload.isLoading;
    },

    setError: (state, action: PayloadAction<{ error?: string }>) => {
      state.ui.error = action.payload.error;
    },

    setSplitViewRatio: (state, action: PayloadAction<{ ratio: number }>) => {
      state.ui.splitViewRatio = Math.max(0.2, Math.min(0.8, action.payload.ratio));
    },

    // Update normalized article state directly (from editor onChange)
    setArticleState: (state, action: PayloadAction<{ article: ArticleState }>) => {
      state.metadata.isDirty = true;
      state.articleState = action.payload.article;
    },

    // References operations for UI panel & importers
    addReference: (state, action: PayloadAction<{ ref: ReferenceEntry }>) => {
      const refs = state.articleState?.references || [];
      const next = [...refs, action.payload.ref];
      if (!state.articleState) state.articleState = { meta: {}, blocks: [], references: next };
      else state.articleState.references = next;
      state.metadata.isDirty = true;
    },
    updateReference: (state, action: PayloadAction<{ id: string; patch: Partial<ReferenceEntry> }>) => {
      if (!state.articleState) return;
      state.articleState.references = (state.articleState.references || []).map((r) =>
        r.id === action.payload.id ? { ...r, ...action.payload.patch } : r,
      );
      state.metadata.isDirty = true;
    },
    removeReference: (state, action: PayloadAction<{ id: string }>) => {
      if (!state.articleState) return;
      state.articleState.references = (state.articleState.references || []).filter((r) => r.id !== action.payload.id);
      state.metadata.isDirty = true;
    },

    // Pending HTML to import into Lexical (client-side docx parsing)
    setPendingHTML: (state, action: PayloadAction<{ html?: string }>) => {
      state.pendingHTML = action.payload.html;
    },

    },
});

export const {
  loadDocument,
  updateEditorState,
  updateJATSField,
  setActiveEditor,
  setPreviewMode,
  setLoading,
  setError,
  setSplitViewRatio,
  setArticleState,
  addReference,
  updateReference,
  removeReference,
  setPendingHTML,
} = documentSlice.actions;

export default documentSlice.reducer;
