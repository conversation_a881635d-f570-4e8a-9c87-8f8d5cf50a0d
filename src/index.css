@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Lexical Editor Styles */
.lexical-editor-wrapper {
  position: relative;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, '.SFNSText-Regular', sans-serif;
}

.editor-wrapper {
  position: relative;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, '.SFNSText-Regular', sans-serif;
}

.editor-header {
  flex-shrink: 0;
}

.editor-shell {
  background: #fff;
  overflow: hidden;
  border: none;
}

.editor-container {
  position: relative;
  background: #fff;
  height: 100%;
}

.editor-input {
  min-height: 100%;
  resize: none;
  font-size: 15px;
  caret-color: rgb(5, 5, 5);
  position: relative;
  tab-size: 1;
  outline: 0;
  padding: 20px;
  border: 0;
  line-height: 1.6;
  font-family: inherit;
  height: 100%;
  overflow-y: auto;
}

.editor-placeholder {
  color: #999;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  top: 20px;
  left: 20px;
  font-size: 15px;
  -webkit-user-select: none;
  user-select: none;
  display: inline-block;
  pointer-events: none;
  white-space: pre-line;
  max-width: calc(100% - 40px);
}

/* Toolbar Styles */
.toolbar {
  display: flex;
  background: #f8f9fa;
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
  min-height: 48px;
}

.toolbar button.toolbar-item {
  border: 1px solid transparent;
  display: flex;
  background: #fff;
  border-radius: 6px;
  padding: 6px 8px;
  cursor: pointer;
  vertical-align: middle;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  font-size: 14px;
  transition: all 0.15s ease;
}

.toolbar button.toolbar-item:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.toolbar button.toolbar-item.spaced {
  margin-right: 2px;
}

.toolbar button.toolbar-item:hover:not([disabled]) {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.toolbar button.toolbar-item.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.toolbar .divider {
  width: 1px;
  background-color: #d1d5db;
  margin: 0 6px;
  height: 20px;
}

.toolbar select.toolbar-item {
  border: 1px solid #d1d5db;
  display: flex;
  background: #fff;
  border-radius: 6px;
  padding: 6px 8px;
  vertical-align: middle;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  min-width: 120px;
  font-size: 14px;
  color: #374151;
  text-overflow: ellipsis;
  cursor: pointer;
  transition: all 0.15s ease;
}

.toolbar select.toolbar-item:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

/* Toolbar Icons */
.toolbar i.format {
  background-size: contain;
  display: inline-block;
  height: 18px;
  width: 18px;
  margin-top: 2px;
  vertical-align: -0.25em;
  display: flex;
  opacity: 0.6;
}

.toolbar i.format.undo {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 7v6h6"/><path d="m21 17-6-6-6 6"/><path d="m3 13 6-6 6 6"/></svg>');
}

.toolbar i.format.redo {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 7v6h-6"/><path d="m3 17 6-6 6 6"/><path d="m21 13-6-6-6 6"/></svg>');
}

.toolbar i.format.bold {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"/><path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"/></svg>');
}

.toolbar i.format.italic {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="19" y1="4" x2="10" y2="4"/><line x1="14" y1="20" x2="5" y2="20"/><line x1="15" y1="4" x2="9" y2="20"/></svg>');
}

.toolbar i.format.underline {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"/><line x1="4" y1="21" x2="20" y2="21"/></svg>');
}

.toolbar i.format.strikethrough {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 4H9a3 3 0 0 0-2.83 4"/><path d="M14 12a4 4 0 0 1 0 8H6"/><line x1="4" y1="12" x2="20" y2="12"/></svg>');
}

.toolbar i.format.subscript::before {
  content: "X₂";
  font-size: 14px;
  font-weight: bold;
}

.toolbar i.format.superscript::before {
  content: "X²";
  font-size: 14px;
  font-weight: bold;
}

.toolbar i.format.code {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="16,18 22,12 16,6"/><polyline points="8,6 2,12 8,18"/></svg>');
}

.toolbar i.format.link {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/></svg>');
}

.toolbar i.format.image {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="8.5" cy="8.5" r="1.5"/><polyline points="21,15 16,10 5,21"/></svg>');
}

.toolbar i.format.equation {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5z"/><path d="M2 17l10 5 10-5"/><path d="M2 12l10 5 10-5"/></svg>');
}

.editor-paragraph {
  margin: 0;
  margin-bottom: 8px;
  position: relative;
}

.editor-paragraph:last-child {
  margin-bottom: 0;
}

.editor-quote {
  margin: 0;
  margin-left: 20px;
  margin-bottom: 10px;
  padding-left: 16px;
  border-left: 4px solid #ccc;
  font-style: italic;
}

.editor-heading-h1 {
  font-size: 24px;
  color: rgb(5, 5, 5);
  font-weight: 400;
  margin: 0;
  margin-bottom: 12px;
  padding: 0;
}

.editor-heading-h2 {
  font-size: 20px;
  color: rgb(5, 5, 5);
  font-weight: 400;
  margin: 0;
  margin-top: 20px;
  margin-bottom: 10px;
  padding: 0;
  text-transform: uppercase;
}

.editor-heading-h3 {
  font-size: 18px;
  color: rgb(5, 5, 5);
  font-weight: 400;
  margin: 0;
  margin-top: 16px;
  margin-bottom: 8px;
  padding: 0;
}

.editor-heading-h4 {
  font-size: 16px;
  color: rgb(5, 5, 5);
  font-weight: 400;
  margin: 0;
  margin-top: 12px;
  margin-bottom: 6px;
  padding: 0;
}

.editor-heading-h5 {
  font-size: 14px;
  color: rgb(5, 5, 5);
  font-weight: 400;
  margin: 0;
  margin-top: 10px;
  margin-bottom: 4px;
  padding: 0;
}

.editor-list-ol {
  padding: 0;
  margin: 0;
  margin-left: 16px;
}

.editor-list-ul {
  padding: 0;
  margin: 0;
  margin-left: 16px;
}

.editor-listitem {
  margin: 8px 32px 8px 32px;
}

.editor-nested-listitem {
  list-style-type: none;
}

.editor-nested-listitem:before {
  content: '';
  position: absolute;
  left: -18px;
  top: 50%;
  height: 2px;
  width: 8px;
  background-color: #999;
}

.editor-link {
  color: rgb(33, 111, 219);
  text-decoration: none;
}

.editor-link:hover {
  text-decoration: underline;
}

.editor-text-bold {
  font-weight: bold;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-underline {
  text-decoration: underline;
}

.editor-text-strikethrough {
  text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
  text-decoration: underline line-through;
}

.editor-text-code {
  background-color: rgb(240, 242, 247);
  padding: 1px 0.25rem;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 94%;
}

.editor-code {
  background-color: rgb(240, 242, 247);
  font-family: Menlo, Consolas, Monaco, monospace;
  display: block;
  padding: 8px 8px 8px 52px;
  line-height: 1.53;
  font-size: 13px;
  margin: 0;
  margin-top: 8px;
  margin-bottom: 8px;
  tab-size: 2;
  /* white-space: pre; */
  overflow-x: auto;
  position: relative;
}

.editor-code:before {
  content: attr(data-gutter);
  position: absolute;
  background-color: #eee;
  left: 0;
  top: 0;
  border-right: 1px solid #ccc;
  padding: 8px;
  color: #777;
  white-space: pre-wrap;
  text-align: right;
  min-width: 25px;
}

.editor-tokenComment {
  color: slategray;
}

.editor-tokenPunctuation {
  color: #999;
}

.editor-tokenProperty {
  color: #905;
}

.editor-tokenSelector {
  color: #690;
}

.editor-tokenOperator {
  color: #9a6e3a;
}

.editor-tokenAttr {
  color: #07a;
}

.editor-tokenVariable {
  color: #e90;
}

.editor-tokenFunction {
  color: #dd4a68;
}

/* Link Editor */
.link-editor {
  position: absolute;
  z-index: 100;
  top: -10000px;
  left: -10000px;
  margin-top: -6px;
  max-width: 300px;
  width: 100%;
  opacity: 0;
  background-color: #fff;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  transition: opacity 0.5s;
}

.link-input {
  display: block;
  width: calc(100% - 24px);
  box-sizing: border-box;
  margin: 8px 12px;
  padding: 8px 12px;
  border-radius: 15px;
  background-color: #eee;
  font-size: 15px;
  color: rgb(5, 5, 5);
  border: 0;
  outline: 0;
  position: relative;
  font-family: inherit;
}

.link-edit {
  background-image: url(images/icons/pencil-fill.svg);
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  width: 35px;
  vertical-align: -0.25em;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  cursor: pointer;
}

.link-input a {
  color: rgb(33, 111, 219);
  text-decoration: none;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  margin-right: 30px;
  text-overflow: ellipsis;
}

.link-input a:hover {
  text-decoration: underline;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
