import React from 'react';

interface PDFPreviewProps {
  url: string;
}

export default function PDFPreview({ url }: PDFPreviewProps) {
  if (!url) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        <div className="text-center">
          <div className="text-4xl mb-4">📋</div>
          <p>No PDF content to preview</p>
          <p className="text-sm mt-2">Upload a document or create content to see the PDF preview</p>
        </div>
      </div>
    );
  }

  // For now, show a placeholder since PDF generation is not fully implemented
  if (url.includes('placeholder')) {
    return (
      <div className="pdf-preview h-full overflow-hidden bg-gray-50">
        <div className="p-4 h-full">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
            <div className="border-b border-gray-200 px-4 py-2 bg-gray-50 rounded-t-lg flex-shrink-0">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-700">PDF Article Preview</h3>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 text-xs font-medium text-amber-600 bg-amber-100 border border-amber-200 rounded">
                    Coming Soon
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-gray-500">
                <div className="text-6xl mb-6">📋</div>
                <h3 className="text-lg font-medium text-gray-700 mb-2">PDF Preview Coming Soon</h3>
                <p className="text-sm text-gray-600 mb-4 max-w-md">
                  PDF generation and preview functionality will be available in a future update. 
                  For now, you can use the HTML preview to see how your article will look.
                </p>
                <div className="space-y-2 text-xs text-gray-500">
                  <p>• High-quality PDF rendering</p>
                  <p>• Multiple article templates</p>
                  <p>• Print-ready formatting</p>
                  <p>• Citation styling</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pdf-preview h-full overflow-hidden bg-gray-50">
      <div className="p-4 h-full">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
          <div className="border-b border-gray-200 px-4 py-2 bg-gray-50 rounded-t-lg flex-shrink-0">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-700">PDF Article Preview</h3>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => window.open(url, '_blank')}
                  className="
                    px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 
                    border border-gray-200 rounded hover:bg-gray-200 
                    transition-colors
                  "
                >
                  🔗 Open in New Tab
                </button>
                <a
                  href={url}
                  download="article.pdf"
                  className="
                    px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 
                    border border-gray-200 rounded hover:bg-gray-200 
                    transition-colors
                  "
                >
                  💾 Download PDF
                </a>
              </div>
            </div>
          </div>
          
          <div className="flex-1 overflow-hidden">
            <iframe
              src={url}
              className="w-full h-full border-0"
              title="PDF Preview"
              style={{
                backgroundColor: 'white',
                borderRadius: '0 0 8px 8px',
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
