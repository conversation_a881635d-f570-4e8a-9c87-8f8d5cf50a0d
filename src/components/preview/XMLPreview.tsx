import React, { useEffect, useRef } from 'react';

interface XMLPreviewProps {
  content: string;
}

export default function XMLPreview({ content }: XMLPreviewProps) {
  const preRef = useRef<HTMLPreElement>(null);

  useEffect(() => {
    if (preRef.current && content) {
      // Simple XML syntax highlighting
      const highlighted = highlightXML(content);
      preRef.current.innerHTML = highlighted;
    }
  }, [content]);

  const highlightXML = (xml: string): string => {
    return xml
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/(&lt;\/?)([\w-]+)(.*?)(&gt;)/g, (match, openTag, tagName, attributes, closeTag) => {
        // Highlight tag names
        const highlightedTag = `${openTag}<span class="xml-tag">${tagName}</span>`;
        
        // Highlight attributes
        const highlightedAttributes = attributes.replace(
          /([\w-]+)(=)(".*?")/g,
          '<span class="xml-attr-name">$1</span><span class="xml-equals">$2</span><span class="xml-attr-value">$3</span>'
        );
        
        return `${highlightedTag}${highlightedAttributes}${closeTag}`;
      })
      // Highlight comments
      .replace(/(&lt;!--.*?--&gt;)/g, '<span class="xml-comment">$1</span>')
      // Highlight DOCTYPE
      .replace(/(&lt;!DOCTYPE.*?&gt;)/g, '<span class="xml-doctype">$1</span>')
      // Highlight XML declaration
      .replace(/(&lt;\?xml.*?\?&gt;)/g, '<span class="xml-declaration">$1</span>');
  };

  if (!content) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        <div className="text-center">
          <div className="text-4xl mb-4">📄</div>
          <p>No XML content to preview</p>
          <p className="text-sm mt-2">Upload a document or create content to see the XML preview</p>
        </div>
      </div>
    );
  }

  return (
    <div className="xml-preview h-full overflow-auto bg-gray-50">
      <div className="p-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200 px-4 py-2 bg-gray-50 rounded-t-lg">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-700">JATS XML Output</h3>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-500">
                  {content.split('\n').length} lines
                </span>
                <span className="text-xs text-gray-500">
                  {(content.length / 1024).toFixed(1)} KB
                </span>
              </div>
            </div>
          </div>
          
          <div className="relative">
            <pre
              ref={preRef}
              className="xml-content p-4 text-sm font-mono leading-relaxed overflow-auto"
              style={{ maxHeight: 'calc(100vh - 200px)' }}
            >
              {content}
            </pre>
          </div>
        </div>
      </div>

      <style>{`
        .xml-preview .xml-tag { color: #0066cc; font-weight: 600; }
        .xml-preview .xml-attr-name { color: #cc6600; }
        .xml-preview .xml-equals { color: #666; }
        .xml-preview .xml-attr-value { color: #009900; }
        .xml-preview .xml-comment { color: #999; font-style: italic; }
        .xml-preview .xml-doctype { color: #666; font-weight: 600; }
        .xml-preview .xml-declaration { color: #666; font-weight: 600; }
        .xml-content { white-space: pre-wrap; word-wrap: break-word; line-height: 1.5; tab-size: 2; }
        .xml-content::-webkit-scrollbar { width: 8px; height: 8px; }
        .xml-content::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 4px; }
        .xml-content::-webkit-scrollbar-thumb { background: #c1c1c1; border-radius: 4px; }
        .xml-content::-webkit-scrollbar-thumb:hover { background: #a8a8a8; }
      `}</style>
    </div>
  );
}
