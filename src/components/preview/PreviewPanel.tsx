import React, { useState, useEffect } from 'react';
import { useDocument } from '../../hooks/useDocument';
import { jatsToXML, jatsToHTML, jatsToPDFUrl } from '../../utils/convert';
import XMLPreview from './XMLPreview';
import HTMLPreview from './HTMLPreview';
import PDFPreview from './PDFPreview';

export default function PreviewPanel() {
  const { jatsDocument, ui, setPreview, setErrorState } = useDocument();
  const [xmlContent, setXmlContent] = useState<string>('');
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // Generate previews when document changes
  useEffect(() => {
    if (jatsDocument && jatsDocument.front.articleMeta.titleGroup.articleTitle) {
      generatePreviews();
    }
  }, [jatsDocument]);

  const generatePreviews = async () => {
    setIsLoading(true);
    setErrorState();

    try {
      // Generate all previews in parallel (pure front-end)
      const [xml, html, pdf] = await Promise.all([
        Promise.resolve(jatsToXML(jatsDocument)),
        Promise.resolve(jatsToHTML(jatsDocument)),
        jatsToPDFUrl(jatsDocument),
      ]);

      setXmlContent(xml);
      setHtmlContent(html);
      setPdfUrl(pdf);
    } catch (error) {
      console.error('Error generating previews:', error);
      setErrorState(error instanceof Error ? error.message : 'Failed to generate previews');
    } finally {
      setIsLoading(false);
    }
  };

  const handleModeChange = (mode: 'xml' | 'html' | 'pdf') => {
    setPreview(mode);
  };

  const renderPreview = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Generating preview...</p>
          </div>
        </div>
      );
    }

    switch (ui.previewMode) {
      case 'xml':
        return <XMLPreview content={xmlContent} />;
      case 'html':
        return <HTMLPreview content={htmlContent} />;
      case 'pdf':
        return <PDFPreview url={pdfUrl} />;
      default:
        return <XMLPreview content={xmlContent} />;
    }
  };

  return (
    <div className="preview-panel h-full flex flex-col bg-white">
      {/* Preview Mode Tabs */}
      <div className="preview-tabs border-b border-gray-200 bg-gray-50">
        <div className="flex">
          {[
            { mode: 'xml' as const, label: 'XML', icon: '📄' },
            { mode: 'html' as const, label: 'HTML', icon: '🌐' },
            { mode: 'pdf' as const, label: 'PDF', icon: '📋' },
          ].map(({ mode, label, icon }) => (
            <button
              key={mode}
              onClick={() => handleModeChange(mode)}
              className={`
                flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors
                ${ui.previewMode === mode
                  ? 'border-primary-500 text-primary-600 bg-white'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              <span>{icon}</span>
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Preview Actions */}
      <div className="preview-actions border-b border-gray-200 px-4 py-2 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <button
              onClick={generatePreviews}
              disabled={isLoading}
              className="
                px-3 py-1 text-xs font-medium text-primary-600 bg-primary-50 
                border border-primary-200 rounded hover:bg-primary-100 
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-colors
              "
            >
              🔄 Refresh
            </button>
            
            {ui.previewMode === 'xml' && xmlContent && (
              <button
                onClick={() => {
                  navigator.clipboard.writeText(xmlContent);
                }}
                className="
                  px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 
                  border border-gray-200 rounded hover:bg-gray-200 
                  transition-colors
                "
              >
                📋 Copy XML
              </button>
            )}
            
            {ui.previewMode === 'html' && htmlContent && (
              <button
                onClick={() => {
                  const blob = new Blob([htmlContent], { type: 'text/html' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = 'article-preview.html';
                  a.click();
                  URL.revokeObjectURL(url);
                }}
                className="
                  px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 
                  border border-gray-200 rounded hover:bg-gray-200 
                  transition-colors
                "
              >
                💾 Download HTML
              </button>
            )}
          </div>

          <div className="text-xs text-gray-500">
            Last updated: {new Date().toLocaleTimeString()}
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="preview-content flex-1 overflow-hidden">
        {ui.error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-red-600">
              <div className="text-4xl mb-4">⚠️</div>
              <p className="font-medium">Preview Error</p>
              <p className="text-sm text-gray-600 mt-2">{ui.error}</p>
              <button
                onClick={generatePreviews}
                className="
                  mt-4 px-4 py-2 text-sm font-medium text-white bg-red-600 
                  rounded hover:bg-red-700 transition-colors
                "
              >
                Try Again
              </button>
            </div>
          </div>
        ) : (
          renderPreview()
        )}
      </div>
    </div>
  );
}
