import React, { useRef, useEffect } from 'react';

interface HTMLPreviewProps {
  content: string;
}

export default function HTMLPreview({ content }: HTMLPreviewProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (iframeRef.current && content) {
      const iframe = iframeRef.current;
      const doc = iframe.contentDocument || iframe.contentWindow?.document;
      
      if (doc) {
        doc.open();
        doc.write(content);
        doc.close();
      }
    }
  }, [content]);

  if (!content) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        <div className="text-center">
          <div className="text-4xl mb-4">🌐</div>
          <p>No HTML content to preview</p>
          <p className="text-sm mt-2">Upload a document or create content to see the HTML preview</p>
        </div>
      </div>
    );
  }

  return (
    <div className="html-preview h-full overflow-hidden bg-gray-50">
      <div className="p-4 h-full">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
          <div className="border-b border-gray-200 px-4 py-2 bg-gray-50 rounded-t-lg flex-shrink-0">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-700">HTML Article Preview</h3>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => {
                    if (iframeRef.current?.contentWindow) {
                      iframeRef.current.contentWindow.print();
                    }
                  }}
                  className="
                    px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 
                    border border-gray-200 rounded hover:bg-gray-200 
                    transition-colors
                  "
                >
                  🖨️ Print
                </button>
                <button
                  onClick={() => {
                    const blob = new Blob([content], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    window.open(url, '_blank');
                    setTimeout(() => URL.revokeObjectURL(url), 100);
                  }}
                  className="
                    px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 
                    border border-gray-200 rounded hover:bg-gray-200 
                    transition-colors
                  "
                >
                  🔗 Open in New Tab
                </button>
              </div>
            </div>
          </div>
          
          <div className="flex-1 overflow-hidden">
            <iframe
              ref={iframeRef}
              className="w-full h-full border-0"
              title="HTML Preview"
              sandbox="allow-same-origin allow-scripts"
              style={{
                backgroundColor: 'white',
                borderRadius: '0 0 8px 8px',
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
