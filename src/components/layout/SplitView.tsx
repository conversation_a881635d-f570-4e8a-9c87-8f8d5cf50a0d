import React, { useState, useRef, useCallback } from 'react';
import { useDocument } from '../../hooks/useDocument';

interface SplitViewProps {
  leftPanel: React.ReactNode;
  rightPanel: React.ReactNode;
  className?: string;
}

export default function SplitView({ leftPanel, rightPanel, className = '' }: SplitViewProps) {
  const { ui, setSplitRatio } = useDocument();
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const container = containerRef.current;
    const rect = container.getBoundingClientRect();
    const newRatio = (e.clientX - rect.left) / rect.width;
    
    // Constrain ratio between 0.2 and 0.8
    const constrainedRatio = Math.max(0.2, Math.min(0.8, newRatio));
    setSplitRatio(constrainedRatio);
  }, [isDragging, setSplitRatio]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const leftWidth = `${ui.splitViewRatio * 100}%`;
  const rightWidth = `${(1 - ui.splitViewRatio) * 100}%`;

  return (
    <div 
      ref={containerRef}
      className={`split-view flex h-full ${className}`}
    >
      {/* Left Panel */}
      <div 
        className="left-panel overflow-hidden bg-white border-r border-gray-200"
        style={{ width: leftWidth }}
      >
        {leftPanel}
      </div>

      {/* Resize Handle */}
      <div
        className={`
          resize-handle w-1 bg-gray-200 hover:bg-primary-400 cursor-col-resize 
          transition-colors duration-200 flex-shrink-0 relative group
          ${isDragging ? 'bg-primary-500' : ''}
        `}
        onMouseDown={handleMouseDown}
      >
        {/* Visual indicator */}
        <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 bg-gray-300 group-hover:bg-primary-500 transition-colors duration-200" />
        
        {/* Hover area for easier grabbing */}
        <div className="absolute inset-y-0 -left-2 -right-2 cursor-col-resize" />
      </div>

      {/* Right Panel */}
      <div 
        className="right-panel overflow-hidden bg-gray-50"
        style={{ width: rightWidth }}
      >
        {rightPanel}
      </div>

      {/* Overlay during dragging */}
      {isDragging && (
        <div className="fixed inset-0 z-50 cursor-col-resize" style={{ pointerEvents: 'none' }} />
      )}
    </div>
  );
}
