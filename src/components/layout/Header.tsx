import React from 'react';
import { useDocument } from '../../hooks/useDocument';

export default function Header() {
  const { metadata, ui, jatsDocument } = useDocument();

  return (
    <header className="header bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Logo and Title */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AS</span>
            </div>
            <h1 className="text-xl font-bold text-gray-900">AME Star</h1>
          </div>
          
          <div className="h-6 w-px bg-gray-300" />
          
          <div className="text-sm text-gray-600">
            AI-Assisted Typesetting Editor
          </div>
        </div>

        {/* Document Info */}
        <div className="flex items-center gap-6">
          {/* Document Title */}
          {jatsDocument.front.articleMeta.titleGroup.articleTitle && (
            <div className="text-sm">
              <div className="font-medium text-gray-900 truncate max-w-xs">
                {jatsDocument.front.articleMeta.titleGroup.articleTitle}
              </div>
              {metadata.fileName && (
                <div className="text-xs text-gray-500">
                  {metadata.fileName}
                </div>
              )}
            </div>
          )}

          {/* Status Indicators */}
          <div className="flex items-center gap-3">
            {/* Dirty State */}
            {metadata.isDirty && (
              <div className="flex items-center gap-1 text-xs text-amber-600">
                <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
                Unsaved changes
              </div>
            )}

            {/* Loading State */}
            {ui.isLoading && (
              <div className="flex items-center gap-1 text-xs text-blue-600">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                Processing...
              </div>
            )}

            {/* Version */}
            <div className="text-xs text-gray-500">
              v{metadata.version}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <button
              className="
                px-3 py-1.5 text-sm font-medium text-gray-700 bg-white 
                border border-gray-300 rounded-md hover:bg-gray-50 
                transition-colors
              "
              onClick={() => {
                // TODO: Implement save functionality
                console.log('Save document');
              }}
            >
              💾 Save
            </button>
            
            <button
              className="
                px-3 py-1.5 text-sm font-medium text-white bg-primary-600 
                border border-primary-600 rounded-md hover:bg-primary-700 
                transition-colors
              "
              onClick={() => {
                // TODO: Implement export functionality
                console.log('Export document');
              }}
            >
              📤 Export
            </button>
          </div>
        </div>
      </div>

      {/* Error Banner */}
      {ui.error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center gap-2">
            <span className="text-red-500">⚠️</span>
            <span className="text-sm text-red-700">{ui.error}</span>
            <button
              onClick={() => {
                // Clear error
                // TODO: Implement error clearing
              }}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </header>
  );
}
