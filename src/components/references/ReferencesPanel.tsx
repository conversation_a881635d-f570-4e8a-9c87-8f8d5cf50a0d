import React, { useMemo, useState } from 'react';
import { useDocument } from '../../hooks/useDocument';
import { ReferenceEntry } from '../../types/articleState';

export default function ReferencesPanel() {
  const { documentState, addRef, updateRef, removeRef } = useDocument();
  const refs = documentState.articleState?.references || [];
  const [draft, setDraft] = useState<ReferenceEntry | null>(null);

  const startAdd = () => setDraft({ id: `r${refs.length + 1}`, authors: [], raw: '' });
  const cancel = () => setDraft(null);
  const save = () => {
    if (!draft) return;
    if (!refs.find((r) => r.id === draft.id)) addRef(draft);
    else updateRef(draft.id, draft);
    setDraft(null);
  };

  return (
    <div className="p-3 border-l border-gray-200 h-full overflow-auto">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold">References</h3>
        <button className="px-2 py-1 text-sm bg-blue-600 text-white rounded" onClick={startAdd}>Add</button>
      </div>

      <ul className="space-y-2">
        {refs.map((r) => (
          <li key={r.id} className="p-2 border rounded">
            <div className="text-xs text-gray-500 mb-1">{r.id}</div>
            <div className="text-sm break-words">{r.raw || [
              r.authors?.map(a => `${a.surname} ${a.givenNames}`).join(', '),
              r.articleTitle,
              r.source,
              r.year && `(${r.year})`,
              r.volume,
              r.fpage && r.lpage ? `${r.fpage}-${r.lpage}` : r.fpage,
              r.doi && `doi:${r.doi}`,
              r.pmid && `pmid:${r.pmid}`
            ].filter(Boolean).join('. ')}</div>
            <div className="mt-2 flex gap-2">
              <button className="px-2 py-1 text-xs bg-gray-100 rounded" onClick={() => setDraft(r)}>Edit</button>
              <button className="px-2 py-1 text-xs bg-red-100 text-red-600 rounded" onClick={() => removeRef(r.id)}>Delete</button>
            </div>
          </li>
        ))}
      </ul>

      {draft && (
        <div className="fixed inset-0 bg-black/30 flex items-center justify-center">
          <div className="bg-white p-4 rounded w-full max-w-xl space-y-2">
            <h4 className="font-semibold">Reference</h4>
            <div className="grid grid-cols-2 gap-2">
              <label className="text-xs">ID<input className="input" value={draft.id} onChange={(e)=>setDraft({...draft!, id:e.target.value})}/></label>
              <label className="text-xs">Year<input className="input" value={draft.year||''} onChange={(e)=>setDraft({...draft!, year:e.target.value})}/></label>
              <label className="col-span-2 text-xs">Article Title<input className="input" value={draft.articleTitle||''} onChange={(e)=>setDraft({...draft!, articleTitle:e.target.value})}/></label>
              <label className="col-span-2 text-xs">Source (Journal)<input className="input" value={draft.source||''} onChange={(e)=>setDraft({...draft!, source:e.target.value})}/></label>
              <label className="text-xs">Volume<input className="input" value={draft.volume||''} onChange={(e)=>setDraft({...draft!, volume:e.target.value})}/></label>
              <label className="text-xs">First Page<input className="input" value={draft.fpage||''} onChange={(e)=>setDraft({...draft!, fpage:e.target.value})}/></label>
              <label className="text-xs">Last Page<input className="input" value={draft.lpage||''} onChange={(e)=>setDraft({...draft!, lpage:e.target.value})}/></label>
              <label className="text-xs">DOI<input className="input" value={draft.doi||''} onChange={(e)=>setDraft({...draft!, doi:e.target.value})}/></label>
              <label className="text-xs">PMID<input className="input" value={draft.pmid||''} onChange={(e)=>setDraft({...draft!, pmid:e.target.value})}/></label>
              <label className="col-span-2 text-xs">Raw Mixed Citation<textarea className="input min-h-[80px]" value={draft.raw||''} onChange={(e)=>setDraft({...draft!, raw:e.target.value})}/></label>
            </div>
            <div className="flex justify-end gap-2 pt-2">
              <button className="px-3 py-1 text-sm" onClick={cancel}>Cancel</button>
              <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded" onClick={save}>Save</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Very small CSS helpers (tailwind-like expecting global styles)
// .input class relies on global utility styles; replace with your system as needed.

