import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useDocument } from '../../hooks/useDocument';
import { loadDemoDocument } from '../../utils/demoLoader';
import { useAppDispatch } from '../../hooks/redux';
import { setPendingHTML } from '../../store/documentSlice';
// Use the browser build of mammoth for client-side parsing
// @ts-ignore - browser build has no types but exposes the same API
import * as mammoth from 'mammoth/mammoth.browser';
import type { JATSDocument } from '../../types/jats';

export default function DocumentUpload() {
  const { loadDoc, setLoadingState, setErrorState } = useDocument();
  const dispatch = useAppDispatch();
  const [uploadProgress, setUploadProgress] = useState(0);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    if (!file.name.endsWith('.docx')) {
      setErrorState('Please upload a .docx file');
      return;
    }

    try {
      setLoadingState(true);
      setErrorState();
      setUploadProgress(0);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Read file and convert to HTML entirely in the browser
      const arrayBuffer = await file.arrayBuffer();
      const { value: html } = await mammoth.convertToHtml({ arrayBuffer });

      // Build a minimal JATS to trigger editor view (title = file name)
      const minimalJATS: JATSDocument = {
        articleType: 'research-article',
        dtdVersion: '1.2',
        language: 'en',
        front: {
          journalMeta: { journalIds: [], journalTitle: '', abbrevJournalTitle: '', publisher: { name: '' } },
          articleMeta: {
            articleIds: [],
            articleCategories: [],
            titleGroup: { articleTitle: file.name.replace(/\.docx$/i, '') },
            contributors: [],
            affiliations: [],
            authorNotes: [],
            correspondences: [],
            pubDates: [],
            volume: '', issue: '', firstPage: '', lastPage: '', history: [],
            permissions: { copyrightStatement: '', copyrightYear: '', copyrightHolder: '', license: { href: '', content: '' } },
            abstract: { sections: [] },
            keywordGroups: [], fundingGroups: [], customMeta: [],
          },
        },
        body: { sections: [] },
        back: { references: [] },
      };

      // Load document and stash HTML for Lexical import
      loadDoc(minimalJATS, file.name);
      dispatch(setPendingHTML({ html }));

      clearInterval(progressInterval);
      setUploadProgress(100);
    } catch (error) {
      console.error('Upload error:', error);
      setErrorState(error instanceof Error ? error.message : 'Failed to open .docx');
    } finally {
      setLoadingState(false);
      setTimeout(() => setUploadProgress(0), 1000);
    }
  }, [loadDoc, setLoadingState, setErrorState]);

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  return (
    <div className="document-upload p-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Upload Your Document
          </h2>
          <p className="text-gray-600">
            Upload a .docx file to get started with AI-assisted typesetting and JATS XML conversion
          </p>
        </div>

        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
            transition-colors duration-200
            ${isDragActive && !isDragReject
              ? 'border-primary-400 bg-primary-50'
              : isDragReject
              ? 'border-red-400 bg-red-50'
              : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
            }
          `}
        >
          <input {...getInputProps()} />
          
          <div className="space-y-4">
            <div className="text-4xl">
              {isDragActive ? '📁' : '📄'}
            </div>
            
            <div>
              {isDragActive ? (
                <p className="text-lg font-medium text-primary-600">
                  Drop your document here
                </p>
              ) : (
                <>
                  <p className="text-lg font-medium text-gray-900">
                    Drag & drop your .docx file here
                  </p>
                  <p className="text-gray-500">
                    or click to browse files
                  </p>
                </>
              )}
            </div>

            {uploadProgress > 0 && (
              <div className="w-full max-w-xs mx-auto">
                <div className="bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  Uploading... {uploadProgress}%
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="mt-6 text-center text-sm text-gray-500">
          <p>Supported format: Microsoft Word (.docx)</p>
          <p>Maximum file size: 10MB</p>
        </div>

        {/* Demo Button */}
        <div className="mt-6 text-center">
          <button
            type="button"
            onClick={() => {
              const demoDoc = loadDemoDocument();
              loadDoc(demoDoc, 'demo-article.xml');
            }}
            className="
              px-4 py-2 text-sm font-medium text-primary-600 bg-primary-50
              border border-primary-200 rounded-md hover:bg-primary-100
              transition-colors
            "
          >
            🚀 Try Demo Document
          </button>
          <p className="text-xs text-gray-500 mt-2">
            Load a sample research article to explore the editor
          </p>
        </div>

        {/* Features */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl mb-2">🤖</div>
            <h3 className="font-medium text-gray-900 mb-1">AI-Powered Parsing</h3>
            <p className="text-sm text-gray-600">
              Advanced AI extracts and structures your document content automatically
            </p>
          </div>
          
          <div className="text-center">
            <div className="text-2xl mb-2">✏️</div>
            <h3 className="font-medium text-gray-900 mb-1">Rich Text Editing</h3>
            <p className="text-sm text-gray-600">
              Edit content with a powerful rich text editor with real-time preview
            </p>
          </div>
          
          <div className="text-center">
            <div className="text-2xl mb-2">📋</div>
            <h3 className="font-medium text-gray-900 mb-1">JATS XML Export</h3>
            <p className="text-sm text-gray-600">
              Export to industry-standard JATS XML format for academic publishing
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
