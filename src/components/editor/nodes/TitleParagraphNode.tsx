import type { EditorConfig, LexicalNode, <PERSON>de<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./TitleParagraphNode.css";

export class TitleParagraphNode extends ParagraphNode {
  static getType(): string {
    return 'title';
  }

  static clone(node: TitleParagraphNode): TitleParagraphNode {
    return new TitleParagraphNode(node.__key as <PERSON><PERSON><PERSON><PERSON>);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('title-line');
    return dom;
  }
}

export function $createTitleParagraphNode(): TitleParagraphNode {
  return new TitleParagraphNode();
}

export function $isTitleParagraphNode(
  node: LexicalNode | null | undefined,
): node is TitleParagraphNode {
  return node instanceof TitleParagraphNode;
}
