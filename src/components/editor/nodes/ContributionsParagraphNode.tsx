import type { EditorConfig, LexicalNode, <PERSON>de<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./ContributionsParagraphNode.css";

export class ContributionsParagraphNode extends ParagraphNode {
  static getType(): string {
    return 'contributions';
  }

  static clone(node: ContributionsParagraphNode): ContributionsParagraphNode {
    return new ContributionsParagraphNode(node.__key as <PERSON><PERSON><PERSON><PERSON>);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('contributions-line');
    return dom;
  }
}

export function $createContributionsParagraphNode(): ContributionsParagraphNode {
  return new ContributionsParagraphNode();
}

export function $isContributionsParagraphNode(
  node: LexicalNode | null | undefined,
): node is ContributionsParagraphNode {
  return node instanceof ContributionsParagraphNode;
}
