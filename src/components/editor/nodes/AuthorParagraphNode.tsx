import type {EditorConfig, LexicalNode, <PERSON>de<PERSON><PERSON>} from 'lexical';
import { ParagraphNode } from 'lexical';
import "./AuthorParagraphNode.css";

export class AuthorParagraphNode extends ParagraphNode {
  static getType(): string {
    // Use the same key that <PERSON><PERSON>bar mapping expects
    return 'editor<PERSON><PERSON><PERSON>';
  }

  static clone(node: AuthorParagraphNode): AuthorParagraphNode {
    return new AuthorParagraphNode(node.__key as <PERSON>de<PERSON><PERSON>);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('author-line');
    return dom;
  }
}

export function $createAuthorParagraphNode(): AuthorParagraphNode {
  return new AuthorParagraphNode();
}

export function $isAuthorParagraphNode(
  node: LexicalNode | null | undefined,
): node is AuthorParagraphNode {
  return node instanceof AuthorParagraphNode;
}
