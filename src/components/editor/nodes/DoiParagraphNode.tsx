import type { EditorConfig, LexicalNode, Node<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./DoiParagraphNode.css";

export class DoiParagraphNode extends ParagraphNode {
  static getType(): string {
    return 'doi';
  }

  static clone(node: DoiParagraphNode): DoiParagraphNode {
    return new DoiParagraphNode(node.__key as <PERSON><PERSON><PERSON><PERSON>);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('doi-line');
    return dom;
  }
}

export function $createDoiParagraphNode(): DoiParagraphNode {
  return new DoiParagraphNode();
}

export function $isDoiParagraphNode(
  node: LexicalNode | null | undefined,
): node is DoiParagraphNode {
  return node instanceof DoiParagraphNode;
}
