import type { EditorConfig, LexicalNode, <PERSON>de<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./ArticleMetaParagraphNode.css";

export class ArticleMetaParagraphNode extends ParagraphNode {
  static getType(): string {
    // Use the same key that <PERSON><PERSON>bar mapping expects
    return 'articleMeta';
  }

  static clone(node: ArticleMetaParagraphNode): ArticleMetaParagraphNode {
    return new ArticleMetaParagraphNode(node.__key as NodeKey);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('article-meta-line');
    return dom;
  }
}

export function $createArticleMetaParagraphNode(): ArticleMetaParagraphNode {
  return new ArticleMetaParagraphNode();
}

export function $isArticleMetaParagraphNode(
  node: LexicalNode | null | undefined,
): node is ArticleMetaParagraphNode {
  return node instanceof ArticleMetaParagraphNode;
}
