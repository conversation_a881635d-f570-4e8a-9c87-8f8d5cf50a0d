import type { EditorConfig, LexicalNode, Node<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./TitleTypeParagraphNode.css";

export class TitleTypeParagraphNode extends ParagraphNode {
  static getType(): string {
    return 'titleType';
  }

  static clone(node: TitleTypeParagraphNode): TitleTypeParagraphNode {
    return new TitleTypeParagraphNode(node.__key as <PERSON><PERSON><PERSON><PERSON>);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('title-type-line');
    return dom;
  }
}

export function $createTitleTypeParagraphNode(): TitleTypeParagraphNode {
  return new TitleTypeParagraphNode();
}

export function $isTitleTypeParagraphNode(
  node: LexicalNode | null | undefined,
): node is TitleTypeParagraphNode {
  return node instanceof TitleTypeParagraphNode;
}
