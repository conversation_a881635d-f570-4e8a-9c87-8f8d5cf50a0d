import type { EditorConfig, LexicalNode, <PERSON>de<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./AbstractParagraphNode.css";

export class AbstractParagraphNode extends ParagraphNode {
  static getType(): string {
    // Use the same key that <PERSON><PERSON><PERSON> mapping expects
    return 'abstract';
  }

  static clone(node: AbstractParagraphNode): AbstractParagraphNode {
    return new AbstractParagraphNode(node.__key as <PERSON>de<PERSON><PERSON>);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('abstract-line');
    return dom;
  }
}

export function $createAbstractParagraphNode(): AbstractParagraphNode {
  return new AbstractParagraphNode();
}

export function $isAbstractParagraphNode(
  node: LexicalNode | null | undefined,
): node is AbstractParagraphNode {
  return node instanceof AbstractParagraphNode;
}
