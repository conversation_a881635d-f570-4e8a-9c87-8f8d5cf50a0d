import type {EditorConfig, LexicalNode, <PERSON>de<PERSON><PERSON>} from 'lexical';
import { ParagraphNode } from 'lexical';
import "./AffiliationParagraphNode.css";

export class AffiliationParagraphNode extends ParagraphNode {
  static getType(): string {
    // Use the same key that <PERSON><PERSON><PERSON> mapping expects
    return 'affiliation';
  }

  static clone(node: AffiliationParagraphNode): AffiliationParagraphNode {
    return new AffiliationParagraphNode(node.__key as <PERSON>de<PERSON><PERSON>);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('affiliation-line');
    return dom;
  }
}

export function $createAffiliationParagraphNode(): AffiliationParagraphNode {
  return new AffiliationParagraphNode();
}

export function $isAffiliationParagraphNode(
  node: LexicalNode | null | undefined,
): node is AffiliationParagraphNode {
  return node instanceof AffiliationParagraphNode;
}
