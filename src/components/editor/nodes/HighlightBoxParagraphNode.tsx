import type { EditorConfig, LexicalNode, Node<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./HighlightBoxParagraphNode.css";

export class HighlightBoxParagraphNode extends ParagraphNode {
  static getType(): string {
    return 'highlightBox';
  }

  static clone(node: HighlightBoxParagraphNode): HighlightBoxParagraphNode {
    return new HighlightBoxParagraphNode(node.__key as NodeKey);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('highlight-box-line');
    return dom;
  }
}

export function $createHighlightBoxParagraphNode(): HighlightBoxParagraphNode {
  return new HighlightBoxParagraphNode();
}

export function $isHighlightBoxParagraphNode(
  node: LexicalNode | null | undefined,
): node is HighlightBoxParagraphNode {
  return node instanceof HighlightBoxParagraphNode;
}
