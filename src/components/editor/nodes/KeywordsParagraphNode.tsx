import type { EditorConfig, LexicalNode, Node<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./KeywordsParagraphNode.css";

export class KeywordsParagraphNode extends ParagraphNode {
  static getType(): string {
    return 'keywords';
  }

  static clone(node: KeywordsParagraphNode): KeywordsParagraphNode {
    return new KeywordsParagraphNode(node.__key as <PERSON><PERSON><PERSON><PERSON>);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('keywords-line');
    return dom;
  }
}

export function $createKeywordsParagraphNode(): KeywordsParagraphNode {
  return new KeywordsParagraphNode();
}

export function $isKeywordsParagraphNode(
  node: LexicalNode | null | undefined,
): node is KeywordsParagraphNode {
  return node instanceof KeywordsParagraphNode;
}
