import type { EditorConfig, LexicalNode, Node<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./ReferencesParagraphNode.css";

export class ReferencesParagraphNode extends ParagraphNode {
  static getType(): string {
    return 'references';
  }

  static clone(node: ReferencesParagraphNode): ReferencesParagraphNode {
    return new ReferencesParagraphNode(node.__key as <PERSON><PERSON><PERSON><PERSON>);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('references-line');
    return dom;
  }
}

export function $createReferencesParagraphNode(): ReferencesParagraphNode {
  return new ReferencesParagraphNode();
}

export function $isReferencesParagraphNode(
  node: LexicalNode | null | undefined,
): node is ReferencesParagraphNode {
  return node instanceof ReferencesParagraphNode;
}
