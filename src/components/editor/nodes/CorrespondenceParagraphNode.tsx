import type { EditorConfig, LexicalN<PERSON>, <PERSON>de<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./CorrespondenceParagraphNode.css";

export class CorrespondenceParagraphNode extends ParagraphNode {
  static getType(): string {
    return 'correspondence';
  }

  static clone(node: CorrespondenceParagraphNode): CorrespondenceParagraphNode {
    return new CorrespondenceParagraphNode(node.__key as NodeKey);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('correspondence-line');
    return dom;
  }
}

export function $createCorrespondenceParagraphNode(): CorrespondenceParagraphNode {
  return new CorrespondenceParagraphNode();
}

export function $isCorrespondenceParagraphNode(
  node: LexicalNode | null | undefined,
): node is CorrespondenceParagraphNode {
  return node instanceof CorrespondenceParagraphNode;
}
