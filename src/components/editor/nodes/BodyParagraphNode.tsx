import type { EditorConfig, LexicalNode, <PERSON>de<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./BodyParagraphNode.css";

export class BodyParagraphNode extends ParagraphNode {
  static getType(): string {
    return 'body';
  }

  static clone(node: BodyParagraphNode): BodyParagraphNode {
    return new BodyParagraphNode(node.__key as <PERSON><PERSON><PERSON><PERSON>);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('body-line');
    return dom;
  }
}

export function $createBodyParagraphNode(): BodyParagraphNode {
  return new BodyParagraphNode();
}

export function $isBodyParagraphNode(
  node: LexicalNode | null | undefined,
): node is BodyParagraphNode {
  return node instanceof BodyParagraphNode;
}
