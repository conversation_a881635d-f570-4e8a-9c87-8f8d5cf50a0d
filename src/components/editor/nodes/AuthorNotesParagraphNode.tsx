import type { EditorConfig, LexicalNode, <PERSON>de<PERSON><PERSON> } from 'lexical';
import { ParagraphNode } from 'lexical';
import "./AuthorNotesParagraphNode.css";

export class AuthorNotesParagraphNode extends ParagraphNode {
  static getType(): string {
    // Use the same key that <PERSON><PERSON><PERSON> mapping expects
    return 'authorNotes';
  }

  static clone(node: AuthorNotesParagraphNode): AuthorNotesParagraphNode {
    return new AuthorNotesParagraphNode(node.__key as NodeKey);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('author-notes-line');
    return dom;
  }
}

export function $createAuthorNotesParagraphNode(): AuthorNotesParagraphNode {
  return new AuthorNotesParagraphNode();
}

export function $isAuthorNotesParagraphNode(
  node: LexicalNode | null | undefined,
): node is AuthorNotesParagraphNode {
  return node instanceof AuthorNotesParagraphNode;
}
