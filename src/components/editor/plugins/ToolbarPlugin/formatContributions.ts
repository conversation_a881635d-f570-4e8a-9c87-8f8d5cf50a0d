import { $createContributionsParagraphNode } from '../../nodes/ContributionsParagraphNode';
import { $getSelection, $isRangeSelection } from 'lexical';
import { formatParagraph } from './utils';
import { $setBlocksType } from '@lexical/selection';

export function formatContributions(editor: any, blockType: string): void {
  if (blockType !== 'contributions') {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createContributionsParagraphNode());
      }
    });
  } else {
    formatParagraph(editor);
  }
}
