import { LexicalEditor } from 'lexical';
import { $getSelection, $isRangeSelection } from 'lexical';
import { $setBlocksType } from '@lexical/selection';
import { $createBodyParagraphNode } from '../../nodes/BodyParagraphNode';
import { formatParagraph } from './utils';

export const formatBody = (editor: LexicalEditor, blockType: string) => {
  if (blockType !== 'body') {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createBodyParagraphNode());
      }
    });
  } else {
    // 如果已经是 body 格式，则转换回普通段落
    formatParagraph(editor);
  }
};
