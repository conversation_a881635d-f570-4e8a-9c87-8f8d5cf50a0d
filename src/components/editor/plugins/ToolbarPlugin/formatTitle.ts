import { LexicalEditor } from 'lexical';
import { $getSelection, $isRangeSelection } from 'lexical';
import { $setBlocksType } from '@lexical/selection';
import { $createTitleParagraphNode } from '../../nodes/TitleParagraphNode';
import { formatParagraph } from './utils';

export const formatTitle = (editor: LexicalEditor, blockType: string) => {
  if (blockType !== 'title') {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createTitleParagraphNode());
      }
    });
  } else {
    // 如果已经是 title 格式，则转换回普通段落
    formatParagraph(editor);
  }
};
