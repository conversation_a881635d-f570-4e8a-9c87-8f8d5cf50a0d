import { $setBlocksType } from '@lexical/selection';
import { $createCorrespondenceParagraphNode } from '../../nodes/CorrespondenceParagraphNode';
import { $getSelection, $isRangeSelection } from 'lexical';
import { formatParagraph } from './utils';

export function formatCorrespondence(editor: any, blockType: string): void {
  if (blockType !== 'contributions') {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, () => $createCorrespondenceParagraphNode());
        }
      });
    } else {
      formatParagraph(editor);
    }
}
