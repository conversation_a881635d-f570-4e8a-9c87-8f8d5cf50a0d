import { LexicalEditor } from 'lexical';
import { $getSelection, $isRangeSelection } from 'lexical';
import { $setBlocksType } from '@lexical/selection';
import { $createAffiliationParagraphNode } from '../../nodes/AffiliationParagraphNode';
import { formatParagraph } from './utils';

export const formatAffiliation = (editor: LexicalEditor, styleType: string) => {
  if (styleType !== 'affiliation') {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createAffiliationParagraphNode());
      }
    });
  } else {
    // 如果已经是 affiliation 格式，则转换回普通段落
    formatParagraph(editor);
  }
};
