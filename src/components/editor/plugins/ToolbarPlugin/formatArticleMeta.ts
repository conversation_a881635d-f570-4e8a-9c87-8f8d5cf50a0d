import { LexicalEditor } from 'lexical';
import { $getSelection, $isRangeSelection } from 'lexical';
import { $setBlocksType } from '@lexical/selection';
import { $createArticleMetaParagraphNode } from '../../nodes/ArticleMetaParagraphNode';
import { formatParagraph } from './utils';

export const formatArticleMeta = (editor: LexicalEditor, blockType: string) => {
  if (blockType !== 'articleMeta') {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createArticleMetaParagraphNode());
      }
    });
  } else {
    // 如果已经是 articleMeta 格式，则转换回普通段落
    formatParagraph(editor);
  }
};
