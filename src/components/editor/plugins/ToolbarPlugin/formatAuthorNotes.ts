import { LexicalEditor } from 'lexical';
import { $getSelection, $isRangeSelection } from 'lexical';
import { $setBlocksType } from '@lexical/selection';
import { $createAuthorNotesParagraphNode } from '../../nodes/AuthorNotesParagraphNode';
import { formatParagraph } from './utils';

export const formatAuthorNotes = (editor: LexicalEditor, blockType: string) => {
  if (blockType !== 'authorNotes') {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createAuthorNotesParagraphNode());
      }
    });
  } else {
    // 如果已经是 authorNotes 格式，则转换回普通段落
    formatParagraph(editor);
  }
};
