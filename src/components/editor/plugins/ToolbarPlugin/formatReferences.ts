import { LexicalEditor } from 'lexical';
import { $getSelection, $isRangeSelection } from 'lexical';
import { $setBlocksType } from '@lexical/selection';
import { $createReferencesParagraphNode } from '../../nodes/ReferencesParagraphNode';
import { formatParagraph } from './utils';

export const formatReferences = (editor: LexicalEditor, blockType: string) => {
  if (blockType !== 'references') {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createReferencesParagraphNode());
      }
    });
  } else {
    // 如果已经是 references 格式，则转换回普通段落
    formatParagraph(editor);
  }
};
