import { $setBlocksType } from '@lexical/selection';
import { $createHighlightBoxParagraphNode } from '../../nodes/HighlightBoxParagraphNode';
import { $getSelection, $isRangeSelection } from 'lexical';
import { formatParagraph } from './utils';

export function formatHighlightBox(editor: any, blockType: string): void {
  if (blockType !== 'highlightBox') {
        editor.update(() => {
          const selection = $getSelection();
          if ($isRangeSelection(selection)) {
            $setBlocksType(selection, () => $createHighlightBoxParagraphNode());
          }
        });
      } else {
        formatParagraph(editor);
      }
}
