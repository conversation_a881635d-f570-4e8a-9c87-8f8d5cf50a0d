import { LexicalEditor } from 'lexical';
import { $getSelection, $isRangeSelection } from 'lexical';
import { $setBlocksType } from '@lexical/selection';
import { $createAbstractParagraphNode } from '../../nodes/AbstractParagraphNode';
import { formatParagraph } from './utils';

export const formatAbstract = (editor: LexicalEditor, blockType: string) => {
  if (blockType !== 'abstract') {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createAbstractParagraphNode());
      }
    });
  } else {
    // 如果已经是 abstract 格式，则转换回普通段落
    formatParagraph(editor);
  }
};
