import { $setBlocksType } from '@lexical/selection';
import { $createDoiParagraphNode } from '../../nodes/DoiParagraphNode';
import { $getSelection, $isRangeSelection } from 'lexical';
import { formatParagraph } from './utils';

export function formatDoi(editor: any, blockType: string): void {
  if (blockType !== 'doi') {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createDoiParagraphNode());
      }
    });
  } else {
    formatParagraph(editor);
  }
}
