export * from './formatTitleType';
export * from './formatTitle';
export * from './formatBody';
export * from './formatReferences';
export * from './formatAuthorNotes';
export * from './formatArticleMeta';

import { LexicalEditor } from 'lexical';

export interface FormatHandlers {
  formatTitleType: (editor: LexicalEditor, blockType: string) => void;
  formatTitle: (editor: LexicalEditor, blockType: string) => void;
  formatBody: (editor: LexicalEditor, blockType: string) => void;
  formatReferences: (editor: LexicalEditor, blockType: string) => void;
  formatAuthorNotes: (editor: LexicalEditor, blockType: string) => void;
  formatArticleMeta: (editor: LexicalEditor, blockType: string) => void;
}

// 获取当前块类型的辅助函数
export const getBlockTypeFromFormat = (type: string): string => {
  switch (type) {
    case 'titleType':
      return 'titleType';
    case 'authorNotes':
      return 'authorNotes';
    case 'articleMeta':
      return 'articleMeta';
    case 'title':
      return 'title';
    case 'body':
      return 'body';
    case 'references':
      return 'references';
    default:
      return 'paragraph';
  }
};
