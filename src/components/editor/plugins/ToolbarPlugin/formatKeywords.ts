import { $createKeywordsParagraphNode } from '../../nodes/KeywordsParagraphNode';
import { $setBlocksType } from '@lexical/selection';
import { $getSelection, $isRangeSelection } from 'lexical';
import { formatParagraph } from './utils';

export function formatKeywords(editor: any, blockType: string): void {
  if (blockType !== 'keywords') {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, () => $createKeywordsParagraphNode());
        }
      });
    } else {
      // 如果已经是 keywords 格式，则转换回普通段落
      formatParagraph(editor);
    }
}
