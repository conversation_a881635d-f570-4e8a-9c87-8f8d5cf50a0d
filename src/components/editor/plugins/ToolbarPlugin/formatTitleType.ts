import { LexicalEditor } from 'lexical';
import { $getSelection, $isRangeSelection } from 'lexical';
import { $setBlocksType } from '@lexical/selection';
import { $createTitleTypeParagraphNode } from '../../nodes/TitleTypeParagraphNode';
import { formatParagraph } from './utils';

export const formatTitleType = (editor: LexicalEditor, blockType: string) => {
  if (blockType !== 'titleType') {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $setBlocksType(selection, () => $createTitleTypeParagraphNode());
      }
    });
  } else {
    // 如果已经是 titleType 格式，则转换回普通段落
    formatParagraph(editor);
  }
};
