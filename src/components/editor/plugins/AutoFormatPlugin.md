# 自动格式化插件 (AutoFormatPlugin)

## 功能概述

AutoFormatPlugin 是一个智能的文档格式化插件，能够自动分析编辑器中的内容类型并应用相应的格式样式。该插件特别适用于学术论文和研究文档的格式化。

## 主要功能

### 1. 内容类型识别
插件能够自动识别以下内容类型：
- **标题 (Title)**: 文档主标题
- **副标题 (Subtitle)**: 冒号后的副标题
- **作者 (Author)**: 作者姓名
- **机构 (Affiliation)**: 作者所属机构
- **摘要标题 (Abstract Heading)**: "Abstract"、"Summary" 等
- **摘要内容 (Abstract Body)**: 摘要正文内容
- **关键词 (Keywords)**: 关键词列表
- **章节标题 (Section Heading)**: 编号章节标题 (如 "1. Introduction")
- **子章节标题 (Subsection Heading)**: 编号子章节 (如 "1.1 Overview")
- **正文 (Body Text)**: 普通段落文本
- **参考文献标题 (Reference Heading)**: "References"、"Bibliography" 等
- **参考文献条目 (Reference Entry)**: 具体的参考文献
- **DOI**: DOI 信息
- **通讯作者 (Correspondence)**: 通讯作者信息
- **文章元数据 (Article Meta)**: 发表信息等

### 2. 智能分析算法
- **模式匹配**: 使用正则表达式识别特定格式
- **上下文分析**: 考虑内容在文档中的位置
- **长度分析**: 根据文本长度判断内容类型
- **学术关键词检测**: 识别学术术语提高准确性

### 3. 自动格式化
- **导入时自动格式化**: 文档导入后自动应用格式
- **手动格式化**: 点击按钮手动触发格式化
- **格式建议**: 显示格式化建议供用户选择

## 使用方法

### 1. 自动格式化
当您上传 DOCX 文件时，插件会自动：
1. 分析文档内容
2. 识别不同的内容类型
3. 应用相应的格式样式
4. 显示格式化结果

### 2. 手动格式化
1. 点击编辑器右上角的 "🎨 Auto Format" 按钮
2. 插件会分析当前编辑器内容并应用格式
3. 显示格式化完成的通知

### 3. 格式建议
1. 当插件检测到可以改进的格式时，会显示 "💡 X suggestions" 按钮
2. 点击按钮查看具体的格式建议
3. 可以选择应用或忽略每个建议

## 配置选项

插件支持以下配置选项：

```tsx
<AutoFormatPlugin 
  enabled={true}           // 是否启用插件
  autoApply={false}        // 是否自动应用格式化
  showSuggestions={true}   // 是否显示格式建议
/>
```

## 内容识别规则

### 标题识别
- 长度在 10-150 字符之间
- 首字母大写
- 不以句号结尾
- 位于文档开头部分

### 作者识别
- 符合姓名格式 (如 "John Smith", "Smith, J.")
- 位于标题附近

### 摘要识别
- 包含 "Abstract"、"Summary" 等关键词
- 或包含 "Background"、"Methods"、"Results" 等摘要子部分

### 章节标题识别
- 以数字开头 (如 "1. Introduction")
- 全大写文本
- 标准学术章节名称

### 参考文献识别
- 以 [1]、1. 等开头
- 包含年份 (YYYY)
- 符合引用格式

## 技术实现

### 核心文件
- `autoFormatter.ts`: 内容分析和格式化逻辑
- `AutoFormatPlugin.tsx`: React 组件
- `AutoFormatPlugin.css`: 样式文件

### 关键函数
- `analyzeContentType()`: 分析内容类型
- `autoFormatDocument()`: 自动格式化整个文档
- `getFormattingSuggestions()`: 获取格式建议
- `analyzeDocumentStructure()`: 分析文档结构

## 扩展和自定义

### 添加新的内容类型
1. 在 `ContentType` 枚举中添加新类型
2. 在 `CONTENT_PATTERNS` 中定义识别模式
3. 在 `NODE_CREATORS` 中映射到相应的节点创建器

### 自定义识别规则
修改 `CONTENT_PATTERNS` 中的正则表达式来调整识别规则。

### 调整格式化行为
修改 `autoFormatDocument()` 函数来改变格式化逻辑。

## 注意事项

1. **置信度阈值**: 只有置信度 ≥ 0.6 的识别结果才会被应用
2. **性能优化**: 使用防抖机制避免频繁的内容分析
3. **用户体验**: 提供手动控制选项，避免过度自动化
4. **兼容性**: 与现有的编辑器节点类型兼容

## 故障排除

### 格式化不准确
- 检查内容是否符合识别模式
- 调整置信度阈值
- 手动应用正确的格式

### 性能问题
- 减少自动格式化的频率
- 优化正则表达式
- 使用更长的防抖延迟

### 样式问题
- 检查 CSS 文件是否正确加载
- 确认节点类型映射正确
- 验证自定义样式定义

## 更新日志

### v1.0.0
- 初始版本
- 支持基本的内容类型识别
- 实现自动格式化功能
- 添加格式建议系统
