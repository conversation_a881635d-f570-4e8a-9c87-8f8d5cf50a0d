.auto-format-plugin {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  max-width: 300px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.auto-format-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.auto-format-button,
.suggestions-toggle {
  padding: 6px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: #f8f9fa;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
  font-family: inherit;
}

.auto-format-button:hover,
.suggestions-toggle:hover {
  background: #e9ecef;
}

.suggestions-toggle {
  background: #fff3cd;
  border-color: #ffeaa7;
}

.document-structure-info {
  font-size: 11px;
  color: #666;
  margin-bottom: 8px;
  padding: 4px;
  background: #f8f9fa;
  border-radius: 4px;
}

.suggestions-panel {
  border-top: 1px solid #eee;
  padding-top: 8px;
}

.suggestions-panel h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #333;
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 6px;
  margin-bottom: 6px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 11px;
}

.suggestion-content {
  flex: 1;
  margin-right: 8px;
}

.suggestion-text {
  font-weight: 500;
  margin-bottom: 2px;
  color: #333;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.suggestion-details {
  color: #666;
  font-size: 10px;
}

.suggestion-actions {
  display: flex;
  gap: 4px;
}

.apply-suggestion,
.dismiss-suggestion {
  padding: 2px 6px;
  border: 1px solid #ccc;
  border-radius: 3px;
  background: white;
  cursor: pointer;
  font-size: 10px;
  transition: background-color 0.2s;
  font-family: inherit;
}

.apply-suggestion {
  color: #28a745;
  border-color: #28a745;
}

.apply-suggestion:hover {
  background: #28a745;
  color: white;
}

.dismiss-suggestion {
  color: #dc3545;
  border-color: #dc3545;
}

.dismiss-suggestion:hover {
  background: #dc3545;
  color: white;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .auto-format-plugin {
    position: relative;
    top: auto;
    right: auto;
    margin: 10px 0;
    max-width: 100%;
  }
}

/* Integration with editor */
.editor-container .auto-format-plugin {
  position: absolute;
  top: 10px;
  right: 10px;
}

/* Hide when editor is in focus mode */
.editor-container.focus-mode .auto-format-plugin {
  display: none;
}

/* Notification styles for auto-format results */
.auto-format-notification {
  position: fixed;
  top: 50px;
  right: 10px;
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1001;
  animation: slideIn 0.3s ease-out;
}

.auto-format-notification.error {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Content type indicators */
.content-type-indicator {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  margin-left: 4px;
}

.content-type-indicator.title {
  background: #e3f2fd;
  color: #1976d2;
}

.content-type-indicator.author {
  background: #f3e5f5;
  color: #7b1fa2;
}

.content-type-indicator.abstract {
  background: #e8f5e8;
  color: #388e3c;
}

.content-type-indicator.section {
  background: #fff3e0;
  color: #f57c00;
}

.content-type-indicator.reference {
  background: #fce4ec;
  color: #c2185b;
}

.content-type-indicator.body {
  background: #f5f5f5;
  color: #616161;
}
