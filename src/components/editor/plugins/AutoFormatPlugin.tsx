import { useEffect, useState } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  autoFormatDocument,
  analyzeDocumentStructure,
  getFormattingSuggestions,
  ContentType
} from '../utils/autoFormatter';
import './AutoFormatPlugin.css';

interface AutoFormatPluginProps {
  enabled?: boolean;
  autoApply?: boolean;
  showSuggestions?: boolean;
}

export default function AutoFormatPlugin({ 
  enabled = true, 
  autoApply = false,
  showSuggestions = true 
}: AutoFormatPluginProps): JSX.Element | null {
  const [editor] = useLexicalComposerContext();
  const [suggestions, setSuggestions] = useState<Array<{
    nodeKey: string;
    currentType: string;
    suggestedType: ContentType;
    confidence: number;
    text: string;
  }>>([]);
  const [documentStructure, setDocumentStructure] = useState<any>(null);
  const [showSuggestionsPanel, setShowSuggestionsPanel] = useState(false);
  const [notification, setNotification] = useState<string | null>(null);

  // Auto-format on content change
  useEffect(() => {
    if (!enabled) return;

    const removeUpdateListener = editor.registerUpdateListener(({ editorState, dirtyElements }) => {
      // Only run if there are actual content changes
      if (dirtyElements.size === 0) return;

      // Debounce the analysis to avoid excessive processing
      const timeoutId = setTimeout(() => {
        // Analyze document structure
        const structure = analyzeDocumentStructure(editor);
        setDocumentStructure(structure);

        // Get formatting suggestions
        if (showSuggestions) {
          const newSuggestions = getFormattingSuggestions(editor);
          setSuggestions(newSuggestions);
        }

        // Auto-apply formatting if enabled
        if (autoApply && structure.documentType !== 'unknown') {
          autoFormatDocument(editor);
        }
      }, 1000); // 1 second debounce

      return () => clearTimeout(timeoutId);
    });

    return removeUpdateListener;
  }, [editor, enabled, autoApply, showSuggestions]);

  // Manual format function
  const handleManualFormat = () => {
    autoFormatDocument(editor);
    setSuggestions([]); // Clear suggestions after applying
    setNotification('Document formatted successfully!');
    setTimeout(() => setNotification(null), 3000);
  };

  // Apply specific suggestion
  const applySuggestion = (suggestion: typeof suggestions[0]) => {
    // Implementation would apply the specific formatting suggestion
    // For now, we'll just remove it from the list
    setSuggestions(prev => prev.filter(s => s.nodeKey !== suggestion.nodeKey));
  };

  // Dismiss suggestion
  const dismissSuggestion = (nodeKey: string) => {
    setSuggestions(prev => prev.filter(s => s.nodeKey !== nodeKey));
  };

  if (!enabled) return null;

  return (
    <div className="auto-format-plugin">
      {/* Format Button */}
      <div className="auto-format-controls">
        <button
          type="button"
          onClick={handleManualFormat}
          className="auto-format-button"
          title="Auto-format document based on content analysis"
        >
          🎨 Auto Format
        </button>

        {suggestions.length > 0 && (
          <button
            type="button"
            onClick={() => setShowSuggestionsPanel(!showSuggestionsPanel)}
            className="suggestions-toggle"
            title={`${suggestions.length} formatting suggestions available`}
          >
            💡 {suggestions.length} suggestions
          </button>
        )}
      </div>

      {/* Document Structure Info */}
      {documentStructure && (
        <div className="document-structure-info">
          <small>
            Document type: {documentStructure.documentType} |
            Sections: {documentStructure.sectionCount} |
            References: {documentStructure.referenceCount}
          </small>
          <br />
          <small className="unique-constraint-info">
            ℹ️ Unique elements (title, author, abstract, etc.) can only appear once
          </small>
        </div>
      )}

      {/* Suggestions Panel */}
      {showSuggestionsPanel && suggestions.length > 0 && (
        <div className="suggestions-panel">
          <h4>Formatting Suggestions</h4>
          {suggestions.map((suggestion) => (
            <div key={suggestion.nodeKey} className="suggestion-item">
              <div className="suggestion-content">
                <div className="suggestion-text">"{suggestion.text}"</div>
                <div className="suggestion-details">
                  Suggested: <strong>{suggestion.suggestedType}</strong> 
                  (confidence: {Math.round(suggestion.confidence * 100)}%)
                </div>
              </div>
              <div className="suggestion-actions">
                <button
                  type="button"
                  onClick={() => applySuggestion(suggestion)}
                  className="apply-suggestion"
                  title="Apply this formatting suggestion"
                >
                  ✓ Apply
                </button>
                <button
                  type="button"
                  onClick={() => dismissSuggestion(suggestion.nodeKey)}
                  className="dismiss-suggestion"
                  title="Dismiss this suggestion"
                >
                  ✕ Dismiss
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Notification */}
      {notification && (
        <div className="auto-format-notification">
          {notification}
        </div>
      )}
    </div>
  );
}

// Hook for using auto-format functionality
export function useAutoFormat() {
  const [editor] = useLexicalComposerContext();

  const formatDocument = () => {
    autoFormatDocument(editor);
  };

  const getStructure = () => {
    return analyzeDocumentStructure(editor);
  };

  const getSuggestions = () => {
    return getFormattingSuggestions(editor);
  };

  return {
    formatDocument,
    getStructure,
    getSuggestions,
  };
}
