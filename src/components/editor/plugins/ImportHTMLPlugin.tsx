import {useEffect} from 'react';
import {useLexicalComposerContext} from '@lexical/react/LexicalComposerContext';
import {useAppDispatch, useAppSelector} from '../../../hooks/redux';
import {useDocument} from '../../../hooks/useDocument';
import {$getRoot} from 'lexical';
import {$generateNodesFromDOM} from '@lexical/html';
import { setPendingHTML } from '../../../store/documentSlice';

export default function ImportHTMLPlugin(): null {
  const [editor] = useLexicalComposerContext();
  const pendingHTML = useAppSelector((s) => s.document.pendingHTML);
  const dispatch = useAppDispatch();
  const { updateEditor, setErrorState, setPreview } = useDocument();

  useEffect(() => {
    if (!pendingHTML) return;

    const doImport = async () => {
      try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(pendingHTML, 'text/html');
        editor.update(() => {
          const nodes = $generateNodesFromDOM(editor, doc);
          const root = $getRoot();
          root.clear();
          root.append(...nodes);
        });
        // After import, keep Redux unifiedDocument in sync at least once
        updateEditor('unifiedDocument', editor.getEditorState());
        // Clear pending HTML to avoid re-imports on re-mounts
        dispatch(setPendingHTML({ html: undefined }));
        // Optionally switch preview to HTML
        setPreview('html');
      } catch (e: any) {
        console.error('ImportHTMLPlugin: failed to import HTML', e);
        setErrorState(e?.message || 'Failed to import document content');
      }
    };

    doImport();
  }, [pendingHTML, editor, updateEditor, dispatch, setPreview, setErrorState]);

  return null;
}

