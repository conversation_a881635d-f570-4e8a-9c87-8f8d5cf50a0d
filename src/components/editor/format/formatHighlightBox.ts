import { $createHighlightBoxParagraphNode } from '../nodes/HighlightBoxParagraphNode';
import { $getSelection, $isRangeSelection, FORMAT_TEXT_COMMAND } from 'lexical';

export function formatHighlightBox(editor: any): void {
  if (editor.isEditable()) {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const nodes = selection.getNodes();
      if (nodes.length > 0) {
        const highlightBoxNode = $createHighlightBoxParagraphNode();
        const firstNode = nodes[0];
        const topLevelNode = firstNode.getTopLevelElement();
        if (topLevelNode !== null) {
          topLevelNode.insertAfter(highlightBoxNode);
          highlightBoxNode.select();
        }
      }
    }
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'highlightBox');
  }
}
