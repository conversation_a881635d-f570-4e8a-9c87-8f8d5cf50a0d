import { $createDoiParagraphNode } from '../nodes/DoiParagraphNode';
import { $getSelection, $isRangeSelection, FORMAT_TEXT_COMMAND } from 'lexical';

export function formatDoi(editor: any): void {
  if (editor.isEditable()) {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const nodes = selection.getNodes();
      if (nodes.length > 0) {
        const doiNode = $createDoiParagraphNode();
        const firstNode = nodes[0];
        const topLevelNode = firstNode.getTopLevelElement();
        if (topLevelNode !== null) {
          topLevelNode.insertAfter(doiNode);
          doiNode.select();
        }
      }
    }
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'doi');
  }
}
