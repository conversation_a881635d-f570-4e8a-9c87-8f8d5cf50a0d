import { $createCorrespondenceParagraphNode } from '../nodes/CorrespondenceParagraphNode';
import { $getSelection, $isRangeSelection, FORMAT_TEXT_COMMAND } from 'lexical';

export function formatCorrespondence(editor: any): void {
  if (editor.isEditable()) {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const nodes = selection.getNodes();
      if (nodes.length > 0) {
        const correspondenceNode = $createCorrespondenceParagraphNode();
        const firstNode = nodes[0];
        const topLevelNode = firstNode.getTopLevelElement();
        if (topLevelNode !== null) {
          topLevelNode.insertAfter(correspondenceNode);
          correspondenceNode.select();
        }
      }
    }
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'correspondence');
  }
}
