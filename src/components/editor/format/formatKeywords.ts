import { $createKeywordsParagraphNode } from '../nodes/KeywordsParagraphNode';
import { $getSelection, $isRangeSelection, FORMAT_TEXT_COMMAND } from 'lexical';

export function formatKeywords(editor: any): void {
  if (editor.isEditable()) {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const nodes = selection.getNodes();
      if (nodes.length > 0) {
        const keywordsNode = $createKeywordsParagraphNode();
        const firstNode = nodes[0];
        const topLevelNode = firstNode.getTopLevelElement();
        if (topLevelNode !== null) {
          topLevelNode.insertAfter(keywordsNode);
          keywordsNode.select();
        }
      }
    }
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'keywords');
  }
}
