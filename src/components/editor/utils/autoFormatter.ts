import { LexicalEditor, $getRoot, $isParagraphNode, $isTextNode } from 'lexical';
import { $setBlocksType } from '@lexical/selection';
import { $isHeadingNode } from '@lexical/rich-text';

// Import all the custom paragraph node creators
import { $createTitleTypeParagraphNode } from '../nodes/TitleTypeParagraphNode';
import { $createTitleParagraphNode } from '../nodes/TitleParagraphNode';
import { $createAuthorParagraphNode } from '../nodes/AuthorParagraphNode';
import { $createAffiliationParagraphNode } from '../nodes/AffiliationParagraphNode';
import { $createAbstractParagraphNode } from '../nodes/AbstractParagraphNode';
import { $createKeywordsParagraphNode } from '../nodes/KeywordsParagraphNode';
import { $createBodyParagraphNode } from '../nodes/BodyParagraphNode';
import { $createReferencesParagraphNode } from '../nodes/ReferencesParagraphNode';
import { $createArticleMetaParagraphNode } from '../nodes/ArticleMetaParagraphNode';
import { $createCorrespondenceParagraphNode } from '../nodes/CorrespondenceParagraphNode';
import { $createDoiParagraphNode } from '../nodes/DoiParagraphNode';
import { $createContributionsParagraphNode } from '../nodes/ContributionsParagraphNode';

// Content type definitions
export enum ContentType {
  TITLE_TYPE = 'title_type',
  TITLE = 'title',
  SUBTITLE = 'subtitle',
  AUTHOR = 'author',
  AFFILIATION = 'affiliation',
  ABSTRACT_HEADING = 'abstract_heading',
  ABSTRACT_BODY = 'abstract_body',
  KEYWORDS = 'keywords',
  SECTION_HEADING = 'section_heading',
  SUBSECTION_HEADING = 'subsection_heading',
  BODY_TEXT = 'body_text',
  REFERENCE_HEADING = 'reference_heading',
  REFERENCE_ENTRY = 'reference_entry',
  DOI = 'doi',
  CONTRIBUTIONS = 'contributions',
  CORRESPONDENCE = 'correspondence',
  ARTICLE_META = 'article_meta',
  UNKNOWN = 'unknown'
}

// Content analysis patterns
const CONTENT_PATTERNS = {
  [ContentType.TITLE_TYPE]: [
    /^(Original Article|Review|Case Report|Editorial|Letter|Comment|Short Communication|Clinical Trial|Randomized Controlled Trial|Observational Study|Systematic Review|Meta-Analysis|Clinical Guideline|Consensus Statement|Position Paper|Book Review|Conference Report|Editorial Commentary|Erratum|Retraction|Correction|Addendum|Author Response|Peer Review|Editorial Board|Table of Contents|Index|Acknowledgments|Conflict of Interest|Funding Statement|Data Availability Statement|Ethics Statement|Supplementary Material|Appendix|Author Biography|Author Contribution Statement|Reviewer Acknowledgment|Publisher's Note|Copyright Information|Citation Metrics|Related Articles|References Cited|Works Cited|Bibliography|Acknowledgments|Conflict of Interest|Funding Statement|Data Availability Statement|Ethics Statement|Supplementary Material|Appendix|Author Biography|Author Contribution Statement|Reviewer Acknowledgment|Publisher's Note|Copyright Information)/i,
  ],
  [ContentType.TITLE]: [
    /^[A-Z][^.!?]*(?:[:.][^.!?]*)?$/,  // Capitalized text without ending punctuation
    /^.{10,150}$/,  // Moderate length (typical title length)
  ],
  [ContentType.SUBTITLE]: [
    // /:\s*(.+)$/,  // Text after colon (subtitle indicator)
  ],
  [ContentType.AUTHOR]: [
    /^[A-Za-z. ]+(?:\d+(?:,\d+)*#?)?(?:, [A-Za-z. ]+(?:\d+(?:,\d+)*#?)?)*$/,
    /^[A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)*(?:\s*,\s*[A-Z][a-z]+(?:\s+[A-Z][a-z]*\.?)*)*$/,  // Author name patterns
    /^[A-Z][a-z]+\s+[A-Z]\.\s*[A-Z][a-z]+/,  // First M. Last format
    /^([A-Z][a-z]+(\s[A-Z]\.)?(\s[A-Z][a-z]+)?|[\u4e00-\u9fa5]{2,4})(\d+|\^|#|\*|,?\d+)?$/,

  ],
  [ContentType.CONTRIBUTIONS]: [
    /^contributions\s*[:.]?\s*/i,
    /^author\s*contributions\s*[:.]?\s*/i,
    /^funding\s*[:.]?\s*/i,
  ],
  [ContentType.CORRESPONDENCE]: [
    /^correspondence\s*[:.]?\s*/i,
    /^contact\s*[:.]?\s*/i,
    /^corresponding\s*author\s*[:.]?\s*/i,
  ],
  [ContentType.AFFILIATION]: [
    /(?:university|college|institute|department|school|hospital|center|centre)/i,
    /(?:@|email|\.edu|\.org|\.com)/i,  // Email or institutional domains
  ],
  [ContentType.ABSTRACT_HEADING]: [
    /^abstract$/i,
    /^summary$/i,
    /^overview$/i,
  ],
  [ContentType.ABSTRACT_BODY]: [
    /^(?:background|objective|methods?|results?|conclusions?)[:.]?\s*/i,  // Abstract section indicators
  ],
  [ContentType.KEYWORDS]: [
    /^keywords?\s*[:.]?\s*/i,
    /^key\s*words?\s*[:.]?\s*/i,
  ],
  [ContentType.SECTION_HEADING]: [
    /^(\d+\.?\s*)(.*)/,  // Numbered sections (1. Introduction)
    /^(introduction|methods?|methodology|results?|discussion|conclusion|references?)$/i,
    /^([A-Z][A-Z\s]+)$/,  // ALL CAPS headings
    /^(background|literature\s*review|related\s*work)$/i,
  ],
  [ContentType.SUBSECTION_HEADING]: [
    /^(\d+\.\d+\.?\s*)(.*)/,  // Numbered subsections (1.1 Overview)
    /^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*):?\s*$/,  // Title Case headings
  ],
  [ContentType.REFERENCE_HEADING]: [
    /^references?$/i,
    /^bibliography$/i,
    /^works?\s*cited$/i,
  ],
  [ContentType.REFERENCE_ENTRY]: [
    /^\d\.[A-Za-z\s\-]+ [A-Z. ]+(?:, [A-Za-z\s\-]+ [A-Z. ]+)*(?:, et al\.)? [A-Za-z\s:?\-,\[\]]+\. [A-Za-z\s.]+ \d{4};(?:\d+\(\d+\)|\d+):(?:\d+-\d+|\d+)\.$/,
    /^\[\d+\]/,  // [1] Reference format
    /^\d+\./,   // 1. Reference format
    /^[A-Z][a-z]+,\s*[A-Z]/,  // Author, A. format
    /\(\d{4}\)/,  // Contains year in parentheses
  ],
  [ContentType.DOI]: [
    /^doi\s*[:.]?\s*/i,
    /10\.\d{4,}/,  // DOI pattern
  ],
  [ContentType.ARTICLE_META]: [
    /^(?:received|accepted|published|available\s*online)\s*[:.]?\s*/i,
    /^(?:article\s*info|publication\s*info)\s*[:.]?\s*/i,
  ],
};

// Academic keywords for context analysis
const ACADEMIC_KEYWORDS = {
  methodology: ['method', 'methodology', 'approach', 'technique', 'procedure', 'protocol', 'experiment'],
  results: ['result', 'finding', 'outcome', 'data', 'observation', 'measurement', 'statistic'],
  discussion: ['discussion', 'interpretation', 'implication', 'limitation', 'future work', 'conclusion'],
  academic_terms: ['hypothesis', 'theory', 'model', 'framework', 'literature', 'research', 'study'],
};

interface ContentAnalysis {
  contentType: ContentType;
  confidence: number;
  metadata: Record<string, any>;
}

/**
 * Analyze text content to determine its type for appropriate formatting
 */
export function analyzeContentType(text: string, context?: { position?: number; total?: number }): ContentAnalysis {
  const trimmedText = text.trim();
  if (!trimmedText) {
    return { contentType: ContentType.UNKNOWN, confidence: 0, metadata: {} };
  }

  const scores: Record<ContentType, number> = {} as Record<ContentType, number>;
  const matchedTypes = new Set<ContentType>();

  // 定义可重复匹配的内容类型
  const repeatableTypes = new Set([
    ContentType.BODY_TEXT,
    ContentType.REFERENCE_ENTRY,
    ContentType.SECTION_HEADING,
    ContentType.SUBSECTION_HEADING
  ]);

  // 按优先级顺序处理内容类型（将重要的类型放在前面）
  const contentTypePriority = Object.entries(CONTENT_PATTERNS).sort(([typeA], [typeB]) => {
    // 标题类型优先级最高
    if (typeA === ContentType.TITLE_TYPE) return -1;
    if (typeB === ContentType.TITLE_TYPE) return 1;
    // 标题次之
    if (typeA === ContentType.TITLE) return -1;
    if (typeB === ContentType.TITLE) return 1;
    return 0;
  });

  for (const [contentType, patterns] of contentTypePriority) {
    // 如果非重复类型已经匹配过，则跳过
    if (!repeatableTypes.has(contentType as ContentType) && matchedTypes.has(contentType as ContentType)) {
      continue;
    }

    for (const pattern of patterns) {
      if (pattern.test(trimmedText)) {
        scores[contentType as ContentType] = 1;
        if (!repeatableTypes.has(contentType as ContentType)) {
          matchedTypes.add(contentType as ContentType);
        }
        break; // 一旦匹配成功就不再尝试该类型的其他模式
      }
    }
  }

  // 应用基于位置的调整
  if (context) {
    const { position = 0, total = 1 } = context;
    const relativePosition = position / total;

    // 只对已匹配的类型进行位置调整
    if (scores[ContentType.TITLE_TYPE] || scores[ContentType.TITLE]) {
      // 标题类型更可能出现在开头
      if (position <= 1) {
        scores[ContentType.TITLE_TYPE] = (scores[ContentType.TITLE_TYPE] || 0) + 0.1;
        scores[ContentType.TITLE] = (scores[ContentType.TITLE] || 0) + 0.1;
      } else {
        // 如果不在开头，降低可能性
        scores[ContentType.TITLE_TYPE] = (scores[ContentType.TITLE_TYPE] || 0) - 0.1;
        scores[ContentType.TITLE] = (scores[ContentType.TITLE] || 0) - 0.1;
      }
    }

    if (scores[ContentType.REFERENCE_ENTRY] || scores[ContentType.REFERENCE_HEADING]) {
      // 参考文献更可能出现在结尾
      // if (relativePosition > 0.8) {
        scores[ContentType.REFERENCE_ENTRY] = (scores[ContentType.REFERENCE_ENTRY] || 0) + 0.1;
        scores[ContentType.REFERENCE_HEADING] = (scores[ContentType.REFERENCE_HEADING] || 0) + 0.1;
      // }
    }
  }

  // Find the best match
  let bestType = ContentType.BODY_TEXT; // 默认为正文
  let maxScore = 0;

  for (const [type, score] of Object.entries(scores)) {
    if (score > maxScore) {
      maxScore = score;
      bestType = type as ContentType;
    }
  }

  // Generate metadata
  const metadata = generateMetadata(trimmedText, bestType);

  return {
    contentType: bestType,
    confidence: Math.min(maxScore, 1.0),
    metadata,
  };


  function generateMetadata(text: string, contentType: ContentType): Record<string, any> {
    const metadata: Record<string, any> = {
      length: text.length,
      wordCount: text.split(/\s+/).length,
      hasNumbers: /\d/.test(text),
      hasPunctuation: /[.!?;:]/.test(text),
      startsWithCapital: /^[A-Z]/.test(text),
    };

    // Content-type specific metadata
    if (contentType === ContentType.SECTION_HEADING || contentType === ContentType.SUBSECTION_HEADING) {
      const match = text.match(/^(\d+(?:\.\d+)*)/);
      if (match) {
        metadata.sectionNumber = match[1];
        metadata.sectionLevel = match[1].split('.').length;
      }
    }

    if (contentType === ContentType.REFERENCE_ENTRY) {
      if (text.startsWith('[')) {
        const match = text.match(/^\[(\d+)\]/);
        if (match) {
          metadata.referenceNumber = parseInt(match[1]);
        }
      } else if (/^\d+\./.test(text)) {
        const match = text.match(/^(\d+)\./);
        if (match) {
          metadata.referenceNumber = parseInt(match[1]);
        }
      }
    }

    // Find academic keywords
    metadata.academicKeywords = findAcademicKeywords(text);

    return metadata;
  }

}

/**
 * Find academic keywords in the text
 */
function findAcademicKeywords(text: string): Record<string, string[]> {
  const found: Record<string, string[]> = {};
  const textLower = text.toLowerCase();

  for (const [category, keywords] of Object.entries(ACADEMIC_KEYWORDS)) {
    const foundKeywords = keywords.filter(keyword => textLower.includes(keyword));
    if (foundKeywords.length > 0) {
      found[category] = foundKeywords;
    }
  }

  return found;
}

/**
 * Map content types to node creators
 */
// Track which unique content types have already been found
const UNIQUE_CONTENT_TYPES = new Set([
  ContentType.TITLE_TYPE,
  ContentType.TITLE,
  ContentType.SUBTITLE,
  ContentType.AUTHOR,
  ContentType.AFFILIATION,
  ContentType.ABSTRACT_HEADING,
  ContentType.KEYWORDS,
  ContentType.REFERENCE_HEADING,
  ContentType.DOI,
  ContentType.CORRESPONDENCE,
  ContentType.ARTICLE_META,
]);

const NODE_CREATORS: Partial<Record<ContentType, () => any>> = {
  [ContentType.TITLE_TYPE]: $createTitleTypeParagraphNode,
  [ContentType.TITLE]: $createTitleParagraphNode,
  [ContentType.AUTHOR]: $createAuthorParagraphNode,
  [ContentType.AFFILIATION]: $createAffiliationParagraphNode,
  [ContentType.ABSTRACT_HEADING]: $createAbstractParagraphNode,
  [ContentType.ABSTRACT_BODY]: $createAbstractParagraphNode,
  [ContentType.KEYWORDS]: $createKeywordsParagraphNode,
  [ContentType.BODY_TEXT]: $createBodyParagraphNode,
  [ContentType.REFERENCE_HEADING]: $createReferencesParagraphNode,
  [ContentType.REFERENCE_ENTRY]: $createReferencesParagraphNode,
  [ContentType.DOI]: $createDoiParagraphNode,
  [ContentType.CORRESPONDENCE]: $createCorrespondenceParagraphNode,
  [ContentType.CONTRIBUTIONS]: $createContributionsParagraphNode,
  [ContentType.ARTICLE_META]: $createArticleMetaParagraphNode,
};

/**
 * Apply automatic formatting to a node based on content analysis
 */
export function applyAutoFormat(editor: LexicalEditor, nodeKey: string, text: string, context?: { position?: number; total?: number }): void {
  const analysis = analyzeContentType(text, context);

  // Only apply formatting if confidence is high enough
  if (analysis.confidence < 0.6) {
    return;
  }

  const nodeCreator = NODE_CREATORS[analysis.contentType];
  if (!nodeCreator) {
    return;
  }

  editor.update(() => {
    const root = $getRoot();
    const children = root.getChildren();
    const node = children.find(child => child.getKey() === nodeKey);
    if (node && $isParagraphNode(node)) {
      const newNode = nodeCreator();

      // Copy all children (text nodes, etc.)
      const nodeChildren = node.getChildren();
      newNode.append(...nodeChildren);

      // Replace the node
      node.replace(newNode);
    }
  });
}

/**
 * Automatically format all content in the editor based on content analysis
 */
/**
 * Analyze document structure to improve content type detection
 */
export function analyzeDocumentStructure(editor: LexicalEditor): {
  hasTitle: boolean;
  hasAbstract: boolean;
  hasSections: boolean;
  hasReferences: boolean;
  sectionCount: number;
  referenceCount: number;
  documentType: string;
} {
  let structure = {
    hasTitle: false,
    hasAbstract: false,
    hasSections: false,
    hasReferences: false,
    sectionCount: 0,
    referenceCount: 0,
    documentType: 'unknown',
  };

  editor.getEditorState().read(() => {
    const root = $getRoot();
    const children = root.getChildren();

    children.forEach((node, index) => {
      if ($isParagraphNode(node) || $isHeadingNode(node)) {
        const textContent = node.getTextContent().trim();
        if (textContent) {
          const analysis = analyzeContentType(textContent, {
            position: index,
            total: children.length,
          });

          switch (analysis.contentType) {
            case ContentType.TITLE:
              if (index < 3) structure.hasTitle = true;
              break;
            case ContentType.ABSTRACT_BODY:
            case ContentType.ABSTRACT_HEADING:
              structure.hasAbstract = true;
              break;
            case ContentType.SECTION_HEADING:
            case ContentType.SUBSECTION_HEADING:
              structure.hasSections = true;
              structure.sectionCount++;
              break;
            case ContentType.REFERENCE_ENTRY:
            case ContentType.REFERENCE_HEADING:
              structure.hasReferences = true;
              if (analysis.contentType === ContentType.REFERENCE_ENTRY) {
                structure.referenceCount++;
              }
              break;
          }
        }
      }
    });

    // Determine document type
    if (structure.hasAbstract && structure.sectionCount >= 3 && structure.hasReferences) {
      structure.documentType = 'academic_paper';
    } else if (structure.hasSections && structure.hasReferences) {
      structure.documentType = 'research_article';
    } else if (structure.sectionCount >= 2) {
      structure.documentType = 'structured_document';
    } else {
      structure.documentType = 'simple_document';
    }
  });

  return structure;
}

/**
 * Get node type string from content type
 */
function getNodeTypeFromContentType(contentType: ContentType): string {
  const typeMap: Record<ContentType, string> = {
    [ContentType.TITLE_TYPE]: 'title-type-paragraph',
    [ContentType.TITLE]: 'title-paragraph',
    [ContentType.AUTHOR]: 'author-paragraph',
    [ContentType.AFFILIATION]: 'affiliation-paragraph',
    [ContentType.ABSTRACT_HEADING]: 'abstract-paragraph',
    [ContentType.ABSTRACT_BODY]: 'abstract-paragraph',
    [ContentType.KEYWORDS]: 'keywords-paragraph',
    [ContentType.BODY_TEXT]: 'body-paragraph',
    [ContentType.REFERENCE_HEADING]: 'references-paragraph',
    [ContentType.REFERENCE_ENTRY]: 'references-paragraph',
    [ContentType.DOI]: 'doi-paragraph',
    [ContentType.CORRESPONDENCE]: 'correspondence-paragraph',
    [ContentType.CONTRIBUTIONS]: 'contributions-paragraph',
    [ContentType.ARTICLE_META]: 'article-meta-paragraph',
    [ContentType.SUBTITLE]: 'title-paragraph',
    [ContentType.SECTION_HEADING]: 'paragraph', // Use default heading
    [ContentType.SUBSECTION_HEADING]: 'paragraph', // Use default heading
    [ContentType.UNKNOWN]: 'paragraph',
  };

  return typeMap[contentType] || 'paragraph';
}

/**
 * Get formatting suggestions for the current document
 */
export function getFormattingSuggestions(editor: LexicalEditor): Array<{
  nodeKey: string;
  currentType: string;
  suggestedType: ContentType;
  confidence: number;
  text: string;
}> {
  const suggestions: Array<{
    nodeKey: string;
    currentType: string;
    suggestedType: ContentType;
    confidence: number;
    text: string;
  }> = [];

  editor.getEditorState().read(() => {
    const root = $getRoot();
    const children = root.getChildren();

    children.forEach((node, index) => {
      if ($isParagraphNode(node)) {
        const textContent = node.getTextContent().trim();
        if (textContent) {
          const analysis = analyzeContentType(textContent, {
            position: index,
            total: children.length,
          });

          // Only suggest if confidence is reasonable and it's not already formatted correctly
          if (analysis.confidence >= 0.5 && analysis.contentType !== ContentType.BODY_TEXT) {
            const currentType = node.getType();
            const suggestedNodeType = getNodeTypeFromContentType(analysis.contentType);

            if (currentType !== suggestedNodeType) {
              suggestions.push({
                nodeKey: node.getKey(),
                currentType,
                suggestedType: analysis.contentType,
                confidence: analysis.confidence,
                text: textContent.substring(0, 100) + (textContent.length > 100 ? '...' : ''),
              });
            }
          }
        }
      }
    });
  });

  return suggestions;
}

export function autoFormatDocument(editor: LexicalEditor): void {
  editor.update(() => {
    const root = $getRoot();
    const children = root.getChildren();
    const totalNodes = children.length;

    // First pass: collect all potential assignments with confidence scores
    const potentialAssignments: Array<{
      node: any;
      index: number;
      contentType: ContentType;
      confidence: number;
      textContent: string;
    }> = [];

    children.forEach((node, index) => {
      if ($isParagraphNode(node) && !$isHeadingNode(node)) {
        const textContent = node.getTextContent().trim();
        if (textContent) {
          const analysis = analyzeContentType(textContent, {
            position: index,
            total: totalNodes,
          });

          if (analysis.confidence >= 0.6) {
            potentialAssignments.push({
              node,
              index,
              contentType: analysis.contentType,
              confidence: analysis.confidence,
              textContent,
            });
          }
        }
      }
    });

    // Second pass: resolve conflicts for unique types
    const assignedUniqueTypes = new Set<ContentType>();
    const finalAssignments: Array<{
      node: any;
      contentType: ContentType;
    }> = [];

    // Sort by confidence (highest first) and position (earlier first for ties)
    potentialAssignments.sort((a, b) => {
      if (Math.abs(a.confidence - b.confidence) > 0.1) {
        return b.confidence - a.confidence; // Higher confidence first
      }
      return a.index - b.index; // Earlier position first for similar confidence
    });

    potentialAssignments.forEach((assignment) => {
      const { node, contentType } = assignment;

      // Check if this is a unique type that has already been assigned
      if (UNIQUE_CONTENT_TYPES.has(contentType)) {
        if (assignedUniqueTypes.has(contentType)) {
          // This unique type is already assigned, treat as body text
          finalAssignments.push({
            node,
            contentType: ContentType.BODY_TEXT,
          });
          return;
        } else {
          // First occurrence of this unique type
          assignedUniqueTypes.add(contentType);
        }
      }

      finalAssignments.push({
        node,
        contentType,
      });
    });

    // Third pass: apply the final assignments
    finalAssignments.forEach(({ node, contentType }) => {
      const nodeCreator = NODE_CREATORS[contentType];
      if (nodeCreator) {
        const newNode = nodeCreator();

        // Copy all children (text nodes, etc.)
        const children = node.getChildren();
        newNode.append(...children);

        // Replace the node
        node.replace(newNode);
      }
    });
  });
}