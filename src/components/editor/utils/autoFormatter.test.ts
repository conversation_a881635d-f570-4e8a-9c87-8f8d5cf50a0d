import { analyzeContentType, ContentType } from './autoFormatter';

describe('AutoFormatter', () => {
  describe('analyzeContentType', () => {
    test('should identify title correctly', () => {
      const result = analyzeContentType('A Novel Approach to Machine Learning', { position: 0, total: 10 });
      expect(result.contentType).toBe(ContentType.TITLE);
      expect(result.confidence).toBeGreaterThan(0.6);
    });

    test('should identify author correctly', () => {
      const result = analyzeContentType('<PERSON>, <PERSON>', { position: 1, total: 10 });
      expect(result.contentType).toBe(ContentType.AUTHOR);
      expect(result.confidence).toBeGreaterThan(0.5);
    });

    test('should identify abstract heading', () => {
      const result = analyzeContentType('Abstract', { position: 3, total: 10 });
      expect(result.contentType).toBe(ContentType.ABSTRACT_HEADING);
      expect(result.confidence).toBeGreater<PERSON>han(0.7);
    });

    test('should identify section headings', () => {
      const result = analyzeContentType('1. Introduction', { position: 5, total: 10 });
      expect(result.contentType).toBe(ContentType.SECTION_HEADING);
      expect(result.confidence).toBeGreaterThan(0.6);
    });

    test('should identify references', () => {
      const result = analyzeContentType('[1] Smith, J. (2023). A paper. Journal, 1(1), 1-10.', { position: 8, total: 10 });
      expect(result.contentType).toBe(ContentType.REFERENCE_ENTRY);
      expect(result.confidence).toBeGreaterThan(0.6);
    });

    test('should identify keywords', () => {
      const result = analyzeContentType('Keywords: machine learning, artificial intelligence', { position: 4, total: 10 });
      expect(result.contentType).toBe(ContentType.KEYWORDS);
      expect(result.confidence).toBeGreaterThan(0.7);
    });

    test('should identify affiliation', () => {
      const result = analyzeContentType('Department of Computer Science, University of Technology', { position: 2, total: 10 });
      expect(result.contentType).toBe(ContentType.AFFILIATION);
      expect(result.confidence).toBeGreaterThan(0.5);
    });

    test('should identify DOI', () => {
      const result = analyzeContentType('DOI: 10.1234/example.doi', { position: 9, total: 10 });
      expect(result.contentType).toBe(ContentType.DOI);
      expect(result.confidence).toBeGreaterThan(0.7);
    });

    test('should default to body text for unclear content', () => {
      const result = analyzeContentType('This is some regular paragraph text that does not match any specific pattern.', { position: 6, total: 10 });
      expect(result.contentType).toBe(ContentType.BODY_TEXT);
    });

    test('should handle empty text', () => {
      const result = analyzeContentType('', { position: 0, total: 1 });
      expect(result.contentType).toBe(ContentType.UNKNOWN);
      expect(result.confidence).toBe(0);
    });
  });

  describe('Unique type constraints', () => {
    test('should prefer earlier position for title when multiple candidates exist', () => {
      const title1 = analyzeContentType('First Potential Title', { position: 0, total: 10 });
      const title2 = analyzeContentType('Second Potential Title', { position: 2, total: 10 });
      
      // Both should be identified as titles, but position 0 should have higher effective score
      expect(title1.contentType).toBe(ContentType.TITLE);
      expect(title2.contentType).toBe(ContentType.TITLE);
      
      // The context adjustment should make the first one more confident
      expect(title1.confidence).toBeGreaterThanOrEqual(title2.confidence);
    });

    test('should identify subsection headings', () => {
      const result = analyzeContentType('1.1 Background and Motivation', { position: 6, total: 10 });
      expect(result.contentType).toBe(ContentType.SUBSECTION_HEADING);
      expect(result.confidence).toBeGreaterThan(0.6);
    });

    test('should handle correspondence information', () => {
      const result = analyzeContentType('Correspondence: <EMAIL>', { position: 9, total: 10 });
      expect(result.contentType).toBe(ContentType.CORRESPONDENCE);
      expect(result.confidence).toBeGreaterThan(0.6);
    });

    test('should identify article metadata', () => {
      const result = analyzeContentType('Received: January 1, 2023; Accepted: February 1, 2023', { position: 9, total: 10 });
      expect(result.contentType).toBe(ContentType.ARTICLE_META);
      expect(result.confidence).toBeGreaterThan(0.6);
    });
  });

  describe('Context-based adjustments', () => {
    test('should boost title confidence at beginning', () => {
      const resultEarly = analyzeContentType('Research Title', { position: 0, total: 10 });
      const resultLate = analyzeContentType('Research Title', { position: 8, total: 10 });
      
      expect(resultEarly.confidence).toBeGreaterThan(resultLate.confidence);
    });

    test('should boost reference confidence at end', () => {
      const resultEarly = analyzeContentType('[1] Reference entry', { position: 1, total: 10 });
      const resultLate = analyzeContentType('[1] Reference entry', { position: 9, total: 10 });
      
      expect(resultLate.confidence).toBeGreaterThan(resultEarly.confidence);
    });

    test('should boost abstract confidence in middle-early position', () => {
      const resultVeryEarly = analyzeContentType('Abstract', { position: 0, total: 10 });
      const resultOptimal = analyzeContentType('Abstract', { position: 3, total: 10 });
      const resultLate = analyzeContentType('Abstract', { position: 8, total: 10 });
      
      expect(resultOptimal.confidence).toBeGreaterThan(resultVeryEarly.confidence);
      expect(resultOptimal.confidence).toBeGreaterThan(resultLate.confidence);
    });
  });

  describe('Length-based adjustments', () => {
    test('should penalize very long titles', () => {
      const shortTitle = analyzeContentType('Short Title', { position: 0, total: 10 });
      const longTitle = analyzeContentType('This is an extremely long title that goes on and on and probably should not be considered a title because it is way too verbose and detailed for a typical academic paper title', { position: 0, total: 10 });
      
      expect(shortTitle.confidence).toBeGreaterThan(longTitle.confidence);
    });

    test('should boost body text confidence for longer content', () => {
      const shortText = analyzeContentType('Short text.', { position: 5, total: 10 });
      const longText = analyzeContentType('This is a much longer piece of text that contains multiple sentences and provides detailed information about the research methodology and findings. It clearly represents body text content rather than any specific structural element.', { position: 5, total: 10 });
      
      // Both might be identified as body text, but longer text should have higher confidence
      if (shortText.contentType === ContentType.BODY_TEXT && longText.contentType === ContentType.BODY_TEXT) {
        expect(longText.confidence).toBeGreaterThan(shortText.confidence);
      }
    });
  });
});
