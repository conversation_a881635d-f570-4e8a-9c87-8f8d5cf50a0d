# 自动格式化唯一类型约束示例

## 问题描述

在之前的版本中，自动格式化可能会错误地将多个段落识别为同一种唯一类型（如标题、作者等），导致文档中出现多个标题或多个作者段落，这显然是不合理的。

## 解决方案

新的自动格式化逻辑引入了**唯一类型约束**，确保以下类型在文档中只能出现一次：

### 唯一类型列表
- `TITLE_TYPE` - 文档类型标题
- `TITLE` - 主标题  
- `SUBTITLE` - 副标题
- `AUTHOR` - 作者信息
- `AFFILIATION` - 机构信息
- `ABSTRACT_HEADING` - 摘要标题
- `KEYWORDS` - 关键词
- `REFERENCE_HEADING` - 参考文献标题
- `DOI` - DOI信息
- `CORRESPONDENCE` - 通讯作者
- `ARTICLE_META` - 文章元数据

### 可重复类型
- `ABSTRACT_BODY` - 摘要内容（可以有多段）
- `SECTION_HEADING` - 章节标题（可以有多个章节）
- `SUBSECTION_HEADING` - 子章节标题（可以有多个子章节）
- `BODY_TEXT` - 正文（可以有多段）
- `REFERENCE_ENTRY` - 参考文献条目（可以有多条）

## 工作原理

### 1. 三阶段处理
```typescript
// 第一阶段：收集所有潜在分配
const potentialAssignments = [];

// 第二阶段：解决唯一类型冲突
// - 按置信度排序（高置信度优先）
// - 相同置信度时按位置排序（早出现优先）
// - 唯一类型只分配给第一个候选者

// 第三阶段：应用最终分配
finalAssignments.forEach(applyFormatting);
```

### 2. 冲突解决策略
当多个段落都被识别为同一唯一类型时：
- **优先级1**: 置信度更高的段落
- **优先级2**: 位置更靠前的段落（如果置信度相近）
- **降级处理**: 后续冲突的段落被重新分类为正文

### 3. 示例场景

#### 场景1：多个标题候选
```
段落1 (位置0): "研究论文标题" -> 置信度 0.8 -> 分配为 TITLE
段落3 (位置3): "另一个标题样式" -> 置信度 0.7 -> 降级为 BODY_TEXT
```

#### 场景2：多个作者段落
```
段落1 (位置1): "张三, 李四" -> 置信度 0.9 -> 分配为 AUTHOR  
段落4 (位置4): "王五, 赵六" -> 置信度 0.8 -> 降级为 BODY_TEXT
```

#### 场景3：正常的多段落内容
```
段落5: "1. 引言" -> 分配为 SECTION_HEADING ✓
段落8: "2. 方法" -> 分配为 SECTION_HEADING ✓  
段落12: "3. 结果" -> 分配为 SECTION_HEADING ✓
```

## 配置和自定义

### 修改唯一类型集合
```typescript
const UNIQUE_CONTENT_TYPES = new Set([
  ContentType.TITLE,
  ContentType.AUTHOR,
  // 添加或移除类型...
]);
```

### 调整置信度阈值
```typescript
if (analysis.confidence >= 0.6) { // 可调整阈值
  // 应用格式化
}
```

### 自定义优先级逻辑
```typescript
// 当前排序逻辑
potentialAssignments.sort((a, b) => {
  if (Math.abs(a.confidence - b.confidence) > 0.1) {
    return b.confidence - a.confidence; // 置信度优先
  }
  return a.index - b.index; // 位置优先
});
```

## 用户体验改进

### 1. 可视化反馈
- 插件界面显示："ℹ️ Unique elements (title, author, abstract, etc.) can only appear once"
- 文档结构信息显示识别到的元素类型和数量

### 2. 智能建议
- 当检测到潜在冲突时，在建议面板中显示
- 用户可以手动调整错误的分类

### 3. 透明度
- 测试文件提供了详细的行为验证
- 文档说明了所有决策逻辑

## 测试覆盖

新的测试套件验证了：
- ✅ 基本内容类型识别
- ✅ 唯一类型约束执行
- ✅ 位置优先级处理
- ✅ 置信度比较逻辑
- ✅ 上下文调整效果
- ✅ 长度影响分析

## 性能优化

### 1. 批量处理
- 一次性分析所有段落，避免重复遍历
- 预先排序减少后续比较开销

### 2. 早期退出
- 置信度过低的内容直接跳过
- 已分配的唯一类型快速检查

### 3. 内存效率
- 使用Set进行O(1)查找
- 及时清理临时数据结构

这个改进确保了自动格式化的准确性和可靠性，避免了正则识别错误导致的重复格式化问题。
