import axios from 'axios';
import { JATSDocument } from '../types/jats';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface DocumentResponse {
  success: boolean;
  message: string;
  jats_document?: JATSDocument;
  filename?: string;
  error?: string;
}

export interface ConversionResponse {
  xml?: string;
  html?: string;
  pdf_url?: string;
}

export interface EnhancementResponse {
  enhanced_content: any;
}

export class APIService {
  static async uploadDocx(file: File): Promise<DocumentResponse> {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await api.post('/api/upload-docx', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.detail || 'Failed to upload document');
      }
      throw error;
    }
  }

  static async convertToXML(jatsDocument: JATSDocument): Promise<string> {
    try {
      const response = await api.post('/api/convert-to-xml', jatsDocument);
      return response.data.xml;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.detail || 'Failed to convert to XML');
      }
      throw error;
    }
  }

  static async convertToHTML(jatsDocument: JATSDocument): Promise<string> {
    try {
      const response = await api.post('/api/convert-to-html', jatsDocument);
      return response.data.html;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.detail || 'Failed to convert to HTML');
      }
      throw error;
    }
  }

  static async convertToPDF(jatsDocument: JATSDocument): Promise<string> {
    try {
      const response = await api.post('/api/convert-to-pdf', jatsDocument);
      return response.data.pdf_url;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.detail || 'Failed to convert to PDF');
      }
      throw error;
    }
  }

  static async enhanceContent(content: any): Promise<any> {
    try {
      const response = await api.post('/api/ai-enhance', content);
      return response.data.enhanced_content;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.detail || 'Failed to enhance content');
      }
      throw error;
    }
  }

  static async healthCheck(): Promise<{ status: string; version: string }> {
    try {
      const response = await api.get('/health');
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error('Backend service is not available');
      }
      throw error;
    }
  }
}

export default APIService;
