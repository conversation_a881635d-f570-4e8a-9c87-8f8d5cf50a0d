// Minimal BibTeX parser for common fields
import { ReferenceEntry, PersonName } from '../types/articleState';

export function parseBibTeX(text: string): ReferenceEntry[] {
  const entries: ReferenceEntry[] = [];
  const items = text.split(/@/g).filter(Boolean);
  for (const item of items) {
    const bodyMatch = item.match(/\{([\s\S]*)\}$/);
    if (!bodyMatch) continue;
    const body = bodyMatch[1];
    const id = (body.match(/^\s*([^,]+),/)?.[1] || '').trim() || `r${entries.length+1}`;
    const get = (key: string) =>
      (body.match(new RegExp(key + '\\s*=\\s*[\\
