import { EditorState, $getRoot, LexicalNode, ElementNode, TextNode, $isTextNode, $isElementNode, $isParagraphNode } from 'lexical';
import { ArticleState, Block, Inline, InlineMark, ReferenceEntry, HeadingBlock, ParagraphBlock, ListBlock, TableBlock, FigureBlock, EquationBlock, HrBlock, TableCell, InlineLink } from '../types/articleState';
import { Affiliation, AuthorNote, Contributor, Correspondence, JATSDocument, Section } from '../types/jats';
import { $isHeadingNode, HeadingNode } from '@lexical/rich-text';
import { $isListNode, $isListItemNode, ListNode, ListItemNode } from '@lexical/list';
import { $isLinkNode } from '@lexical/link';
import { $isTableNode, $isTableRowNode, $isTableCellNode, TableNode, TableRowNode, TableCellNode } from '@lexical/table';
import { $isImageNode } from '../components/editor/nodes/ImageNode';
import { $isEquationNode } from '../components/editor/nodes/EquationNode';
import { useDocument } from '../hooks/useDocument';
import { $isAuthorParagraphNode } from '../components/editor/nodes/AuthorParagraphNode';
import { $isAffiliationParagraphNode } from '../components/editor/nodes/AffiliationParagraphNode';
import { $isAbstractParagraphNode } from '../components/editor/nodes/AbstractParagraphNode';
import { $isBodyParagraphNode } from '../components/editor/nodes/BodyParagraphNode';
import { $isTitleTypeParagraphNode } from '../components/editor/nodes/TitleTypeParagraphNode';
import { $isTitleParagraphNode } from '../components/editor/nodes/TitleParagraphNode';
import { $isDoiParagraphNode } from '../components/editor/nodes/DoiParagraphNode';
import { $isContributionsParagraphNode } from '../components/editor/nodes/ContributionsParagraphNode';
import { $isCorrespondenceParagraphNode } from '../components/editor/nodes/CorrespondenceParagraphNode';


// 创建一个作者信息解析函数
function parseAuthorInfo(authorText: string): Contributor {
  // 匹配模式: "Name [affiliations][orcid]" 
  // 例如: "Xiaoming Fang1#0009-0003-7157-1345"
  const pattern = /^(.+?)(?:([0-9,#]+))?(?:(\d{4}-\d{4}-\d{4}-\d{4}))?$/;
  const match = authorText.trim().match(pattern);

  if (!match) {
    throw new Error(`Invalid author format: ${authorText}`);
  }

  const [_, fullName, affiliations, orcid] = match;

  // 分割姓和名
  const nameParts = fullName.trim().split(' ');
  const surname = nameParts[0];
  const givenNames = nameParts.slice(1).join(' ');

  // 处理附属机构引用
  const affRefs = affiliations ?
    affiliations.split('').filter(char => /[0-9#]/.test(char)) :
    [];

  return {
    type: 'author',
    name: {
      surname,
      givenNames,
    },
    affiliationRefs: affRefs,
    ...(orcid && { orcid: orcid })
  } as Contributor;
}

// 提取作者单位信息
function getAuthorAff(affiliationText: string): Affiliation[] {
  const countryMap: { [key: string]: string } = {
    "Afghanistan": "AF",
    "Albania": "AL",
    "Algeria": "DZ",
    "Andorra": "AD",
    "Angola": "AO",
    "Argentina": "AR",
    "Armenia": "AM",
    "Australia": "AU",
    "Austria": "AT",
    "Azerbaijan": "AZ",
    "Bangladesh": "BD",
    "Belarus": "BY",
    "Belgium": "BE",
    "Belize": "BZ",
    "Benin": "BJ",
    "Bhutan": "BT",
    "Bolivia": "BO",
    "Bosnia and Herzegovina": "BA",
    "Botswana": "BW",
    "Brazil": "BR",
    "Brunei": "BN",
    "Bulgaria": "BG",
    "Burkina Faso": "BF",
    "Cambodia": "KH",
    "Cameroon": "CM",
    "Canada": "CA",
    "Chile": "CL",
    "China": "CN",
    "Colombia": "CO",
    "Costa Rica": "CR",
    "Croatia": "HR",
    "Cuba": "CU",
    "Cyprus": "CY",
    "Czech Republic": "CZ",
    "Denmark": "DK",
    "Dominican Republic": "DO",
    "Ecuador": "EC",
    "Egypt": "EG",
    "Estonia": "EE",
    "Ethiopia": "ET",
    "Finland": "FI",
    "France": "FR",
    "Germany": "DE",
    "Greece": "GR",
    "Hong Kong": "HK",
    "Hungary": "HU",
    "Iceland": "IS",
    "India": "IN",
    "Indonesia": "ID",
    "Iran": "IR",
    "Iraq": "IQ",
    "Ireland": "IE",
    "Israel": "IL",
    "Italy": "IT",
    "Japan": "JP",
    "Jordan": "JO",
    "Kazakhstan": "KZ",
    "Kenya": "KE",
    "Korea": "KR",
    "Kuwait": "KW",
    "Latvia": "LV",
    "Lebanon": "LB",
    "Lithuania": "LT",
    "Luxembourg": "LU",
    "Malaysia": "MY",
    "Mexico": "MX",
    "Mongolia": "MN",
    "Morocco": "MA",
    "Nepal": "NP",
    "Netherlands": "NL",
    "New Zealand": "NZ",
    "Nigeria": "NG",
    "Norway": "NO",
    "Pakistan": "PK",
    "Peru": "PE",
    "Philippines": "PH",
    "Poland": "PL",
    "Portugal": "PT",
    "Qatar": "QA",
    "Romania": "RO",
    "Russia": "RU",
    "Saudi Arabia": "SA",
    "Serbia": "RS",
    "Singapore": "SG",
    "Slovakia": "SK",
    "Slovenia": "SI",
    "South Africa": "ZA",
    "Spain": "ES",
    "Sri Lanka": "LK",
    "Sweden": "SE",
    "Switzerland": "CH",
    "Syria": "SY",
    "Taiwan": "TW",
    "Thailand": "TH",
    "Tunisia": "TN",
    "Turkey": "TR",
    "Ukraine": "UA",
    "United Arab Emirates": "AE",
    "United Kingdom": "GB",
    "United States": "US",
    "Uruguay": "UY",
    "Uzbekistan": "UZ",
    "Venezuela": "VE",
    "Vietnam": "VN"
  };


  // 匹配规则： [id][内容](以 ; 或结尾分隔)
  const blockPattern = /(\d+)([^;]+)(?=;|$)/g;

  let match;
  const results: Affiliation[] = [];

  while ((match = blockPattern.exec(affiliationText)) !== null) {
    const id = match[1];
    const label = match[1];

    // 拆分逗号
    const parts = match[2].trim().split(",").map(s => s.trim());

    let department, institution, addressLine, country;

    if (parts[0].startsWith("Department")) {
      department = parts[0];
      institution = parts[1];
      country = parts[parts.length - 1];
      addressLine = parts.slice(2, -1).join(", ") || undefined;
    } else {
      department = undefined;
      institution = parts[0];
      country = parts[parts.length - 1];
      addressLine = parts.slice(1, -1).join(", ") || undefined;
    }

    results.push({
      id,
      label,
      department,
      institution,
      addressLine,
      country,
      countryCode: countryMap[country] || undefined
    });
  }
  return results;
}

// Contributions
function getContributions(node: LexicalNode): AuthorNote[] {
  const notes: AuthorNote[] = [];
  const content = node.getTextContent().trim();

  if (content) {
    notes.push({
      id: genId('note'),
      content,
    });
  }

  return notes;
}

// LexicalToJATS
export function lexicalToJATS(updateJATSFieldValue: (field: string, value: any) => void, node: LexicalNode | null | undefined,getJatsDocumentValue: () => JATSDocument): void   {
  const type = node?.getType();
  // clear old jatsvalue
  
  if ($isTitleTypeParagraphNode(node)) {
    updateJATSFieldValue("articleType", node.getTextContent());
  } else if ($isTitleParagraphNode(node)) {
    updateJATSFieldValue("front.articleMeta.titleGroup.articleTitle", node.getTextContent());
  } else if ($isDoiParagraphNode(node)) {
    updateJATSFieldValue("front.articleMeta.articleIds", [{ type: 'doi', value: node.getTextContent() }]);
  } else if ($isHeadingNode(node)) {
    if (node.getTag() === "h1") {
      updateJATSFieldValue("front.articleMeta.titleGroup.articleTitle", node.getTextContent());
      // updateEditor("articleTitle", node.getTextContent());
    }
  } else if ($isAuthorParagraphNode(node)) {
    // console.log("作者段落:", node.getTextContent());
    let authors: Contributor[] = node.getTextContent()
      .split(", ")
      .map(name => name.trim())
      .map(authorText => parseAuthorInfo(authorText));
    // console.log("Parsed Authors: <AUTHORS>
    updateJATSFieldValue("front.articleMeta.contributors", authors);

  } else if ($isAffiliationParagraphNode(node)) {
    // console.log("作者地址:", node.getTextContent());
    let affiliations: Affiliation[] = getAuthorAff(node.getTextContent())
    //  
    // console.log("Parsed Affiliations:", affiliations);
    updateJATSFieldValue("front.articleMeta.affiliations", affiliations);

  } else if ($isContributionsParagraphNode(node)) {
    let authorNotes: AuthorNote[] = getContributions(node);
    // console.log("Parsed Author Notes:", authorNotes);
    updateJATSFieldValue("front.articleMeta.authorNotes", authorNotes);
  } else if ($isCorrespondenceParagraphNode(node)) {
     let correspondences: Correspondence[] = [];
      const content = node.getTextContent().trim();
      correspondences.push({
        id: genId('corr'),
        email: '',
        content: content,
      });
    updateJATSFieldValue("front.articleMeta.correspondences", correspondences);
  } else if ($isAbstractParagraphNode(node)) {
    let jatsDocument = getJatsDocumentValue()
    if (/^Background/i.test(node.getTextContent().trim())) {
      let bodySections = { title: 'Background', content: node.getTextContent() };
      let abstractSections = Object.assign([], jatsDocument.front.articleMeta.abstract.sections ) || [];
      console.log("Background:", abstractSections);
      abstractSections.push(bodySections);
      updateJATSFieldValue("front.articleMeta.abstract.sections", abstractSections);
    } else if (/^Methods/i.test(node.getTextContent().trim())) {
      let bodySections = { title: 'Methods', content: node.getTextContent() };
      let abstractSections = Object.assign([], jatsDocument.front.articleMeta.abstract.sections ) || [];
      console.log("Methods:", abstractSections);
      abstractSections.push(bodySections);
      updateJATSFieldValue("front.articleMeta.abstract.sections", abstractSections);
    } else if (/^Results/i.test(node.getTextContent().trim())) {
      let bodySections = { title: 'Results', content: node.getTextContent() };
      let abstractSections = Object.assign([], jatsDocument.front.articleMeta.abstract.sections ) || [];
      console.log("Results:", abstractSections);
      abstractSections.push(bodySections);
      updateJATSFieldValue("front.articleMeta.abstract.sections", abstractSections);
    } else if (/^Conclusions/i.test(node.getTextContent().trim())) {
      let bodySections = { title: 'Conclusions', content: node.getTextContent() };
      let abstractSections = Object.assign([], jatsDocument.front.articleMeta.abstract.sections ) || [];
      console.log("Conclusions:", abstractSections);
      abstractSections.push(bodySections);
      updateJATSFieldValue("front.articleMeta.abstract.sections", abstractSections);
    }
  } else if ($isBodyParagraphNode(node)) {
    

  } else if ($isTableNode(node)) {
    // console.log("表格开始");
  } else if ($isTableRowNode(node)) {
    // console.log("  表格行");
  } else if ($isTableCellNode(node)) {
    // console.log("    表格单元格:", node.getTextContent());
  } else if ($isImageNode(node)) {
    // console.log("图片:", node.getSrc(), "alt:", node.getAltText?.());
  } else if ($isTextNode(node)) {
    // console.log(
    //   "文本:",
    //   node.getTextContent(),
    //   "样式:",
    //   node.getFormat()
    // );
  } else {
    // console.log("其他节点:", type);
  }

  // ✅ 只有 ElementNode 才能递归
  if ($isElementNode(node)) {
    node.getChildren().forEach((child) => lexicalToJATS(updateJATSFieldValue, child,getJatsDocumentValue));
  }
}




let idCounter = 0;
const genId = (prefix: string) => `${prefix}-${++idCounter}`;

export function lexicalToArticleState(state: EditorState): ArticleState {
  const blocks: Block[] = [];

  state.read(() => {
    idCounter = 0;
    const root = $getRoot();
    const children = root.getChildren();
    for (const node of children) {
      const block = nodeToBlock(node);
      if (block) blocks.push(block);
    }
  });

  const detected = (lexicalToArticleState as any)._detectedReferences as ReferenceEntry[] | undefined;
  return {
    meta: {},
    blocks,
    references: detected || [],
  };
}

function nodeToBlock(node: LexicalNode): Block | null {
  // Heading
  if ($isHeadingNode(node)) {
    console.log("head 1")
    const h = node as HeadingNode;
    const level = (h.getTag() as any)?.replace?.('h', '') ?? 1;
    const inlines = collectInlines(h);
    const block: HeadingBlock = { type: 'heading', id: genId('h'), level: (Number(level) as any) || 1, inlines };
    return block;
  }

  // List
  if ($isListNode(node)) {
    const list = node as ListNode;
    const ordered = list.getListType() !== 'bullet';
    const items: Inline[][] = [];
    for (const li of list.getChildren()) {
      if ($isListItemNode(li)) {
        items.push(collectInlines(li as unknown as ElementNode));
      }
    }
    const block: ListBlock = { type: 'list', id: genId('list'), ordered, items };
    return block;
  }

  // Table
  if ($isTableNode(node)) {
    const table = node as TableNode;
    const rows: TableCell[][] = [];
    for (const row of table.getChildren()) {
      if ($isTableRowNode(row)) {
        const cells: TableCell[] = [];
        for (const cell of (row as TableRowNode).getChildren()) {
          if ($isTableCellNode(cell)) {
            cells.push({ inlines: collectInlines(cell as unknown as ElementNode) });
          }
        }
        rows.push(cells);
      }
    }
    const block: TableBlock = { type: 'table', id: genId('table'), rows };
    return block;
  }

  // Paragraph or other element container
  if (isElement(node)) {
    const inlines = collectInlines(node as ElementNode);
    // Detect empty paragraphs
    const text = inlines.map((i) => ('text' in i ? (i as InlineMark).text : '')).join('');
    const block: ParagraphBlock | HrBlock = (node.getType && node.getType() === 'horizontalrule')
      ? { type: 'hr', id: genId('hr') }
      : { type: 'paragraph', id: genId('p'), inlines };
    // Collapse pure empty paragraphs
    if ('inlines' in block && !text.trim()) {
      // keep as structural spacing if needed; here we skip
      return null;
    }
    return block as Block;
  }

  return null;
}

function collectInlines(container: ElementNode): Inline[] {
  const result: Inline[] = [];
  for (const child of container.getChildren()) {
    if (isText(child)) {
      result.push(textNodeToInline(child as TextNode));
    } else if ($isLinkNode(child)) {
      const linkNode = child as any;
      const href = linkNode.getURL?.() || linkNode.__url || '';
      const childrenInlines = isElement(child)
        ? collectInlines(child as ElementNode)
        : [];
      result.push({ type: 'link', href, children: childrenInlines } as InlineLink);
    } else if ($isImageNode(child)) {
      const img: any = child;
      result.push({ type: 'image', src: img.getSrc?.() || img.__src, alt: img.getAltText?.() || img.__altText });
    } else if ($isEquationNode(child)) {
      const eq: any = child;
      result.push({ type: 'equation', latex: eq.getEquation?.() || eq.__equation, display: eq.getInline?.() ? 'inline' : 'block' });
    } else if (isElement(child)) {
      result.push(...collectInlines(child as ElementNode));
    }
  }
  return result;
}

function textNodeToInline(node: TextNode): InlineMark {
  const text = node.getTextContent();
  const mark: InlineMark = { text };
  // Lexical stores format bitmask; use helpers
  try {
    // hasFormat is available on TextNode
    (node as any).hasFormat && ((['bold', 'italic', 'underline', 'strikethrough', 'code'] as const).forEach((f) => {
      if ((node as any).hasFormat(f)) {
        if (f === 'strikethrough') (mark as any).strike = true; else (mark as any)[f] = true;
      }
    }));
  } catch { }
  return mark;
}

function isElement(n: LexicalNode): n is ElementNode {
  return (n as any).getChildren != null;
}
function isText(n: LexicalNode): n is TextNode {
  return (n as any).getTextContent != null && (n as any).getFormat != null;
}

export function articleStateToJATS(article: ArticleState): JATSDocument {
  // Map meta + blocks (only headings/paragraphs baseline) and references into JATS shell
  const sections: Section[] = [];
  let currentSec: Section | null = null;
  const flush = () => { if (currentSec) { sections.push(currentSec); currentSec = null; } };

  // Heuristic: auto-detect a trailing "References" section
  try {
    const idx = article.blocks.findIndex((b) => b.type === 'heading' &&
      (plainText((b as any).inlines).trim().toLowerCase() === 'references'));
    if (idx >= 0) {
      const refs: ReferenceEntry[] = [];
      for (let i = idx + 1; i < article.blocks.length; i++) {
        const b = article.blocks[i];
        if (b.type === 'heading') break; // next section starts
        const text = plainText((b as any).inlines || []);
        // simple patterns: "Surname, Given. Title. Journal. Year;Volume:Pages. doi:..."
        if (text.trim()) {
          const m = text.match(/^(.*?)\.?\s+(.*?)\.\s+(.*?)\.\s+(\d{4})[;,.]\s*([\d()]+)?[:,.]\s*([\d\-–]+)?/);
          const doi = (text.match(/doi\s*[:\s]\s*([^\s]+)\b/i)?.[1]) || undefined;
          const pmid = (text.match(/pmid\s*[:\s]\s*(\d+)/i)?.[1]) || undefined;
          const authors = (m?.[1] || '').split(/;|,\s+(?=[A-Z])/).filter(Boolean).map((a) => {
            const parts = a.trim().split(',');
            return { surname: parts[0]?.trim() || '', givenNames: (parts[1] || '').trim() } as ReferenceEntry['authors'][number];
          });
          refs.push({
            id: `r${refs.length + 1}`,
            authors,
            articleTitle: m?.[2] || undefined,
            source: m?.[3] || undefined,
            year: m?.[4] || undefined,
            volume: m?.[5] || undefined,
            fpage: m?.[6]?.split(/[-–]/)[0],
            lpage: m?.[6]?.split(/[-–]/)[1],
            doi,
            pmid,
            raw: text,
          });
        }
      }
      // attach into returned ArticleState by returning later
      (lexicalToArticleState as any)._detectedReferences = refs;
    }
  } catch { }

  for (const b of article.blocks) {
    if (b.type === 'heading') {
      flush();
      currentSec = { id: b.id, title: plainText(b.inlines), content: '' };
    } else if (b.type === 'paragraph') {
      if (!currentSec) currentSec = { id: 'sec-1', title: '', content: '' };
      currentSec.content = (currentSec.content || '') + (currentSec.content ? '\n' : '') + plainText(b.inlines);
    }
  }
  flush();

  const backRefs = (article.references || []).map<NonNullable<JATSDocument['back']>['references'][number]>((r, idx) => ({
    id: r.id || `r${idx + 1}`,
    mixedCitation: r.raw ? undefined : {
      publicationType: 'journal',
      authors: r.authors,
      articleTitle: r.articleTitle,
      source: r.source,
      year: r.year,
      volume: r.volume,
      fpage: r.fpage,
      lpage: r.lpage,
      pubIds: [
        ...(r.doi ? [{ type: 'doi', value: r.doi }] : []),
        ...(r.pmid ? [{ type: 'pmid', value: r.pmid }] : []),
      ],
    },
    raw: r.raw,
  }));

  const jats: JATSDocument = {
    articleType: 'research-article',
    dtdVersion: '1.2',
    language: article.meta.language || 'en',
    front: {
      journalMeta: {
        journalIds: [],
        journalTitle: '',
        abbrevJournalTitle: '',
        publisher: { name: '' },
      },
      articleMeta: {
        articleIds: [],
        articleCategories: [],
        titleGroup: { articleTitle: article.meta.title || '' },
        contributors: [],
        affiliations: [],
        authorNotes: [],
        correspondences: [],
        pubDates: [],
        volume: '',
        issue: '',
        firstPage: '',
        lastPage: '',
        history: [],
        permissions: {
          copyrightStatement: '',
          copyrightYear: '',
          copyrightHolder: '',
          license: { href: '', content: '' },
        },
        abstract: { sections: [] },
        keywordGroups: [],
        fundingGroups: [],
        customMeta: [],
      },
    },
    body: { sections },
    back: { references: backRefs },
  };

  return jats;
}

function plainText(inlines: Inline[]): string {
  return inlines.map((i) => ('text' in i ? (i as InlineMark).text : '')).join('');
}

function compactCitation(r: ReferenceEntry): string {
  const authorStr = r.authors?.map((a) => `${a.surname} ${a.givenNames}`).join(', ');
  const pages = r.fpage && r.lpage ? `${r.fpage}-${r.lpage}` : r.fpage || '';
  const vol = r.volume ? `${r.volume}` : '';
  const parts = [authorStr, r.articleTitle, r.source, r.year, vol, pages, r.doi && `doi:${r.doi}`, r.pmid && `pmid:${r.pmid}`];
  return parts.filter(Boolean).join('. ');
}
