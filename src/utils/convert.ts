import { Affiliation, AuthorNote, Contributor, Correspondence, JATSDocument } from '../types/jats';

// Very lightweight, purely front-end converters for preview purposes.
// These are not full JATS-compliant serializers, but enough to visualize content.

export function jatsToXML(jats: JATSDocument): string {
  const esc = (s: string | undefined) =>
    (s ?? '')
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
console.log(jats)
  const title = esc(jats.front.articleMeta.titleGroup.articleTitle);
  const sections = (jats.body.sections || []).map((sec) => `
    <sec${sec.id ? ` id="${esc(sec.id)}"` : ''}${sec.type ? ` sec-type="${esc(sec.type)}"` : ''}>
      ${sec.title ? `<title>${esc(sec.title)}</title>` : ''}
      ${sec.content ? `<p>${esc(sec.content)}</p>` : ''}
    </sec>
  `).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "JATS-journalpublishing1-2.dtd">
<article article-type="${esc(jats.articleType)}" dtd-version="${esc(jats.dtdVersion)}" xml:lang="${esc(jats.language)}">
  <front>
    <journal-meta>
      <journal-title>${esc(jats.front.journalMeta.journalTitle)}</journal-title>
      <publisher>
        <publisher-name>${esc(jats.front.journalMeta.publisher.name)}</publisher-name>
      </publisher>
    </journal-meta>
    <article-meta>
     ${renderPublisherId(jats.front.articleMeta.articleIds.find(id => id.type === 'publisher-id')?.value)}
     ${renderDoi(jats.front.articleMeta.articleIds.find(id => id.type === 'doi')?.value)}
     ${renderTitleType(jats.articleType)}
      <title-group>
        <article-title>${title}</article-title>
      </title-group>
      <contrib-group>
       ${renderContributors(jats.front.articleMeta.contributors)}
       ${renderAffilication(jats.front.articleMeta.affiliations)}
      </contrib-group>
      <author-notes>
      ${renderContributions(jats.front.articleMeta.authorNotes)}
      ${renderCorrespondence(jats.front.articleMeta.correspondences)}
      </author-notes>
    </article-meta>
  </front>
  <body>
${sections}
  </body>
  <back>
    ${renderReferences(jats)}
  </back>
</article>`;
}

function renderTitleType(titleType: string | undefined): string {
  if (!titleType) return '';
  return `<article-categories><subj-group subj-group-type="heading"><subject>${esc(titleType)}</subject></subj-group></article-categories>`;
}

function renderDoi(doi: string | undefined): string {
  if (!doi) return '';
  return `<pub-id pub-id-type="doi">${esc(doi)}</pub-id>`;
}

function renderPublisherId(id: string | undefined): string {
  if (!id) return '';
  return `<pub-id pub-id-type="publisher-id">${esc(id)}</pub-id>`;
}

function renderContributors(contributors: JATSDocument['front']['articleMeta']['contributors']): string {
  if (!contributors || !contributors.length) return '';
  const esc = (s: string | undefined) => (s ?? '')
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');

  const people = contributors.map((c: Contributor) => `
    <contrib contrib-type="${esc(c.type)}">
      <name>
        <surname>${c.name.surname}</surname>
        <given-names>${c.name.givenNames}</given-names>
        ${c.orcid ? `<orcid content-type="orcid" xlink:href="https://orcid.org/${c.orcid}">https://orcid.org/${c.orcid}</orcid>` : ''}
        ${c.affiliationRefs.map((a) => `<xref ref-type="${/#/.test(a) ? 'fn' : 'aff'}" rid="${/#/.test(esc(a)) ? `afn2` : `aff${esc(a)}`}">${esc(a)}</xref>`).join('\n')}
      </name>
    </contrib>
  `).join('\n');

  return `${people}`;
}


const esc = (s: string | undefined) => (s ?? '')
  .replace(/&/g, '&amp;')
  .replace(/</g, '&lt;')
  .replace(/>/g, '&gt;')
  .replace(/"/g, '&quot;')
  .replace(/'/g, '&apos;');
// <aff id="aff1"><label>1</label><institution content-type="dept">School of Biological Science and Medical Engineering</institution>, <institution>Beihang University</institution>, <addr-line>Beijing</addr-line>, <country country="cn">China</country>;</aff>
function renderAffilication(affs: Affiliation[]): string {
  if (!affs || !affs.length) return '';


  const items = affs.map((a: Affiliation) => `
    <aff id="aff${esc(a.id)}">
      <label>${esc(a.id)}</label>
      ${a.department ? `<institution content-type="dept">${esc(a.department)}</institution>,` : ''}
      ${a.institution ? `<institution ${a.department ? 'content-type="dept"' : ''}/>}${esc(a.institution)}</institution>,` : ''}
      ${a.addressLine ? `<addr-line>${esc(a.addressLine)}</addr-line>,` : ''}
      ${a.country ? `<country country="${esc(a.countryCode)}">${esc(a.country)}</country>` : ''}
    </aff>
  `).join('\n');

  return `<aff-group>${items}</aff-group>`;
}

// renderContributions
function renderContributions(notes: AuthorNote[]): string {
  if (!notes || !notes.length) return '';


  const items = notes.map((n: AuthorNote,key) => `
      <fn id="afn${key+1}"><p><italic>Contributions:</italic>${esc(n.content)}</p></fn>
      <fn id="afn2" fn-type="equal"><label>#</label><p>These authors contributed equally to this work.</p></fn>
  `).join('\n');

  return `${items}`;
}

// renderCorrespondence
function renderCorrespondence(corr: Correspondence[]): string {
  // console.log("Rendering Correspondence:", corr);
  if (!corr || !corr.length) return '';
  const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b/g;
 
  const items = corr.map((c: Correspondence) => `
      <corresp id="cor${esc(c.id)}"><p>${c.content.replace(emailRegex, (match) => {
  return `<email xlink:href="${match}">${match}</email>`;
})}</p></corresp>
  `).join('\n');

  return `${items}`;
}

function renderReferences(jats: JATSDocument): string {
  const refs = jats.back?.references || [];
  if (!refs.length) return '';


  const items = refs.map((r, idx) => {
    const id = (r as any).id || `r${idx + 1}`;
    const mc = (r as any).mixedCitation;
    if (mc) {
      const people = (mc.authors || []).map((p: any) => `
        <name><surname>${esc(p.surname)}</surname><given-names>${esc(p.givenNames)}</given-names></name>
      `).join('');
      const pubIds = (mc.pubIds || []).map((pid: any) => `
        <pub-id pub-id-type="${esc(pid.type)}">${esc(pid.value)}</pub-id>
      `).join('');
      return `
    <ref id="${esc(id)}">
      <label>${idx + 1}</label>
      <mixed-citation publication-type="${esc(mc.publicationType || 'journal')}">
        <person-group person-group-type="author">${people}</person-group>
        ${mc.articleTitle ? `<article-title>${esc(mc.articleTitle)}</article-title>` : ''}
        ${mc.source ? `<source>${esc(mc.source)}</source>` : ''}
        ${mc.year ? `<year>${esc(mc.year)}</year>` : ''}
        ${mc.volume ? `<volume>${esc(mc.volume)}</volume>` : ''}
        ${mc.fpage ? `<fpage>${esc(mc.fpage)}</fpage>` : ''}
        ${mc.lpage ? `<lpage>${esc(mc.lpage)}</lpage>` : ''}
        ${pubIds}
      </mixed-citation>
    </ref>`;
    }
    return `
    <ref id="${esc(id)}">
      <label>${idx + 1}</label>
      <mixed-citation>${esc((r as any).raw || '')}</mixed-citation>
    </ref>`;
  }).join('\n');

  return `<ref-list>
  <title>References</title>
  ${items}
</ref-list>`;
}


export function jatsToHTML(jats: JATSDocument): string {
  const escapeHtml = (s: string | undefined) =>
    (s ?? '')
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');

  const title = escapeHtml(jats.front.articleMeta.titleGroup.articleTitle);
  const sections = (jats.body.sections || []).map((sec) => `
      <section class="article-sec">
        ${sec.title ? `<h2>${escapeHtml(sec.title)}</h2>` : ''}
        ${sec.content ? `<p>${escapeHtml(sec.content)}</p>` : ''}
      </section>
  `).join('\n');

  return `<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>${title || 'Article Preview'}</title>
  <style>
    body { font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial; line-height: 1.6; padding: 24px; }
    h1,h2,h3 { line-height: 1.2; }
    .container { max-width: 840px; margin: 0 auto; }
    .meta { color: #6b7280; font-size: 12px; margin-bottom: 12px; }
    .article-sec { margin: 24px 0; }
  </style>
</head>
<body>
  <div class="container">
    <h1>${title || 'Untitled Article'}</h1>
    <div class="meta">Front-end generated HTML preview</div>
    ${sections}
  </div>
</body>
</html>`;
}

// For now, we return a placeholder URL to keep PDF preview working without backend.
// The Preview will show a "Coming Soon" placeholder when it contains 'placeholder'.
export async function jatsToPDFUrl(_jats: JATSDocument): Promise<string> {
  return 'placeholder://pdf';
}

