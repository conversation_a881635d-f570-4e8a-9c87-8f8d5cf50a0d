// JATS XML Parser utility
import { JATSDocument, JournalMeta, ArticleMeta, Body, Back } from '../types/jats';

export class JATSParser {
  static parseXMLToJATS(xmlString: string): JATSDocument {
    const parser = new DOMParser();
    const doc = parser.parseFromString(xmlString, 'text/xml');
    const article = doc.querySelector('article');
    
    if (!article) {
      throw new Error('Invalid JATS XML: No article element found');
    }

    return {
      articleType: article.getAttribute('article-type') || '',
      dtdVersion: article.getAttribute('dtd-version') || '',
      language: article.getAttribute('xml:lang') || 'en',
      front: {
        journalMeta: this.parseJournalMeta(doc),
        articleMeta: this.parseArticleMeta(doc),
      },
      body: this.parseBody(doc),
      back: this.parseBack(doc),
    };
  }

  private static parseJournalMeta(doc: Document): JournalMeta {
    const journalMeta = doc.querySelector('journal-meta');
    if (!journalMeta) throw new Error('No journal-meta found');

    const journalIds = Array.from(journalMeta.querySelectorAll('journal-id')).map(el => ({
      type: el.getAttribute('journal-id-type') as 'publisher-id' | 'nlm-ta',
      value: el.textContent || '',
    }));

    return {
      journalIds,
      journalTitle: journalMeta.querySelector('journal-title')?.textContent || '',
      abbrevJournalTitle: journalMeta.querySelector('abbrev-journal-title')?.textContent || '',
      issnPrint: journalMeta.querySelector('issn[pub-type="ppub"]')?.textContent || undefined,
      issnElectronic: journalMeta.querySelector('issn[pub-type="epub"]')?.textContent || undefined,
      publisher: {
        name: journalMeta.querySelector('publisher-name')?.textContent || '',
      },
    };
  }

  private static parseArticleMeta(doc: Document): ArticleMeta {
    const articleMeta = doc.querySelector('article-meta');
    if (!articleMeta) throw new Error('No article-meta found');

    // Parse article IDs
    const articleIds = Array.from(articleMeta.querySelectorAll('article-id')).map(el => ({
      type: el.getAttribute('pub-id-type') as 'publisher-id' | 'doi',
      value: el.textContent || '',
    }));

    // Parse article categories
    const articleCategories = Array.from(articleMeta.querySelectorAll('subj-group subject')).map(el => ({
      type: el.parentElement?.getAttribute('subj-group-type') || '',
      subject: el.textContent || '',
    }));

    // Parse contributors
    const contributors = Array.from(articleMeta.querySelectorAll('contrib')).map(contrib => {
      const name = contrib.querySelector('name');
      const affiliationRefs = Array.from(contrib.querySelectorAll('xref[ref-type="aff"]')).map(
        ref => ref.getAttribute('rid') || ''
      );
      
      return {
        type: 'author' as const,
        name: {
          surname: name?.querySelector('surname')?.textContent || '',
          givenNames: name?.querySelector('given-names')?.textContent || '',
        },
        orcid: contrib.querySelector('uri[content-type="orcid"]')?.getAttribute('xlink:href'),
        affiliationRefs,
        isCorresponding: contrib.getAttribute('corresp') === 'yes',
      };
    });

    // Parse affiliations
    const affiliations = Array.from(articleMeta.querySelectorAll('aff')).map(aff => ({
      id: aff.getAttribute('id') || '',
      label: aff.querySelector('label')?.textContent || '',
      department: aff.querySelector('institution[content-type="dept"]')?.textContent,
      institution: aff.querySelector('institution:not([content-type])')?.textContent || '',
      addressLine: aff.querySelector('addr-line')?.textContent,
      country: aff.querySelector('country')?.textContent || '',
      countryCode: aff.querySelector('country')?.getAttribute('country'),
    }));

    // Parse abstract
    const abstractEl = articleMeta.querySelector('abstract');
    const abstractSections = Array.from(abstractEl?.querySelectorAll('sec') || []).map(sec => ({
      title: sec.querySelector('title')?.textContent || '',
      content: sec.querySelector('p')?.textContent || '',
    }));

    // Parse keywords
    const keywordGroups = Array.from(articleMeta.querySelectorAll('kwd-group')).map(group => ({
      type: group.getAttribute('kwd-group-type') || '',
      title: group.querySelector('title')?.textContent || '',
      keywords: Array.from(group.querySelectorAll('kwd')).map(kwd => ({
        value: kwd.textContent || '',
      })),
    }));

    return {
      articleIds,
      articleCategories,
      titleGroup: {
        articleTitle: articleMeta.querySelector('article-title')?.textContent || '',
      },
      contributors,
      affiliations,
      authorNotes: [], // TODO: Parse author notes
      correspondences: [], // TODO: Parse correspondences
      pubDates: [], // TODO: Parse publication dates
      volume: articleMeta.querySelector('volume')?.textContent || '',
      issue: articleMeta.querySelector('issue')?.textContent || '',
      firstPage: articleMeta.querySelector('fpage')?.textContent || '',
      lastPage: articleMeta.querySelector('lpage')?.textContent || '',
      history: [], // TODO: Parse history
      permissions: {
        copyrightStatement: '',
        copyrightYear: '',
        copyrightHolder: '',
        license: { href: '', content: '' },
      }, // TODO: Parse permissions
      abstract: { sections: abstractSections },
      keywordGroups,
      fundingGroups: [], // TODO: Parse funding
      customMeta: [], // TODO: Parse custom meta
    };
  }

  private static parseBody(doc: Document): Body {
    const body = doc.querySelector('body');
    if (!body) return { sections: [] };

    const sections = Array.from(body.querySelectorAll('body > sec')).map(sec => 
      this.parseSection(sec)
    );

    return { sections };
  }

  private static parseSection(secEl: Element): any {
    const title = secEl.querySelector('title')?.textContent || '';
    const paragraphs = Array.from(secEl.querySelectorAll('p')).map(p => p.textContent || '');
    const content = paragraphs.join('\n\n');
    
    // Parse subsections
    const subsections = Array.from(secEl.querySelectorAll('sec')).map(subsec => 
      this.parseSection(subsec)
    );

    return {
      id: secEl.getAttribute('id'),
      type: secEl.getAttribute('sec-type'),
      title,
      content,
      subsections: subsections.length > 0 ? subsections : undefined,
      figures: [], // TODO: Parse figures
      tables: [], // TODO: Parse tables
      formulas: [], // TODO: Parse formulas
      lists: [], // TODO: Parse lists
      references: [], // TODO: Parse reference citations
    };
  }

  private static parseBack(doc: Document): Back {
    const back = doc.querySelector('back');
    if (!back) return { references: [] };

    // TODO: Parse references from back matter
    return { references: [] };
  }

  static jatsToXML(jats: JATSDocument): string {
    // TODO: Implement JATS to XML conversion
    // This will be used to export the document back to XML
    return '';
  }
}
