<!doctype html>
<html lang="zh">
    <head>
        <meta charset="UTF-8" />
        <title>论文信息抽取示例</title>
    </head>
    <body>
        <h1>论文信息抽取示例</h1>

        <textarea id="paper" rows="10" cols="80">
这是论文正文示例：
标题：深度学习在医学图像分析中的应用
作者：张三，李四
摘要：本文介绍了深度学习在医学图像分析中的最新进展...
  </textarea
        >

        <button id="extract">提取信息</button>
        <pre id="output"></pre>

        <script type="module">
            import * as tf from "https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.11.0/dist/tf.min.js";
            import { pipeline } from "https://cdn.jsdelivr.net/npm/@xenova/transformers@2.4.0/dist/transformers.min.js";

            async function main() {
                // 创建文本生成 pipeline，使用 t5-small
                const generator = await pipeline(
                    "text2text-generation",
                    "Xenova/t5-small"
                );

                document.getElementById("extract").onclick = async () => {
                    const text = document.getElementById("paper").value;

                    // 将信息抽取任务转化为文本到文本任务
                    const prompt = `extract title, authors, abstract: ${text}`;

                    // 执行生成
                    const output = await generator(prompt, { max_length: 256 });

                    // 输出结果
                    document.getElementById("output").textContent =
                        output[0].generated_text;
                };
            }

            main();

            function splitAuthors(line) {
                // 先去掉多余空格
                line = line.trim();

                // 按逗号分割
                let parts = line.split(",");

                // 合并末尾孤立数字到前一个作者（如 "Ming Liu1,5" -> "Ming Liu1,5"）
                const fixedParts = [];
                for (let i = 0; i < parts.length; i++) {
                    let part = parts[i].trim();

                    // 如果是孤立数字或上标符号，合并到前一个
                    if (/^\d+$/.test(part) && fixedParts.length > 0) {
                        fixedParts[fixedParts.length - 1] += "," + part;
                    } else {
                        fixedParts.push(part);
                    }
                }

                return fixedParts;
            }

            function isAuthorLine(line) {
                const parts = splitAuthors(line);
                console.log(parts)

                // 正则匹配中英文姓名 + 可选上标符号
                const authorRegex =
                    /^([A-Z][a-z]+(\s[A-Z]\.)?(\s[A-Z][a-z]+)?|[\u4e00-\u9fa5]{2,4})(\d+|\^|#|\*|,?\d+)?$/;

                let matchCount = 0;
                for (const part of parts) {
                    if (authorRegex.test(part.trim().replaceAll(/(\d|#|^|\*)+/g,"").replaceAll(/\./g," "))) matchCount++;
                }

                return matchCount / parts.length >= 0.7;
            }

            var auth = `Xiaoming Fang1#, Xinyu Zhang2#, Ping Liu1, Dan Yu1, Andrzej Swierniak3, Carl G. Maki4, Ruichen Gao1, Ming Liu1,5`
            console.log(isAuthorLine(auth));
        </script>
    </body>
</html>
