<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>TensorFlow.js NER Example with Transformers.js</title>
</head>
<body>
    <h1>Extract Paragraphs Containing Person Names</h1>
    <textarea id="inputText" rows="8" cols="60" placeholder="Enter text here..."></textarea><br>
    <button id="extractButton">Extract Paragraphs</button>
    <div id="output"></div>

    <script type="module">
        import { pipeline } from 'https://cdn.jsdelivr.net/npm/@xenova/transformers@2.17.2';

        async function runNER() {
            const inputText = document.getElementById('inputText').value;
            if (!inputText) {
                alert('Please enter some text.');
                return;
            }

            document.getElementById('output').innerHTML = 'Loading model...';

            try {
                // 初始化 NER 模型
                const extractor = await pipeline(
                    'token-classification',
                    'Xenova/bert-base-multilingual-cased-ner-hrl',
                    { quantized: true }
                );

                // 拆分段落（按换行）
                const paragraphs = inputText.split(/\n+/).map(p => p.trim()).filter(Boolean);

                const paragraphsWithNames = [];

                for (const paragraph of paragraphs) {
                    const output = await extractor(paragraph, { aggregation_strategy: 'simple' });

                    // 判断是否包含人名
                    const hasPerson = output.some(ent => ent.entity.toUpperCase().includes('PER'));
                    if (hasPerson) {
                        paragraphsWithNames.push(paragraph);
                    }
                }

                // 渲染结果
                if (paragraphsWithNames.length === 0) {
                    document.getElementById('output').innerHTML = "<p>No paragraphs with person names found.</p>";
                } else {
                    document.getElementById('output').innerHTML = `
                        <h2>Paragraphs with Person Names:</h2>
                        <ul>
                            ${paragraphsWithNames.map(p => `<li>${p}</li>`).join('')}
                        </ul>
                    `;
                }
            } catch (error) {
                console.error(error);
                document.getElementById('output').innerHTML = 'Error: ' + error.message;
            }
        }

        // 按钮事件
        document.getElementById('extractButton').addEventListener('click', runNER);
    </script>
</body>
</html>
