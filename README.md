# AME Star - AI-Assisted Typesetting Editor

A modern AI-assisted typesetting editor with real-time editing and JATS XML conversion. This system supports DOCX → JSON → JATS XML mapping, interactive editing, and multi-format previews.

## Features

- **DOCX Upload & AI Parsing**: Upload .docx files and use AI to parse them into structured JSON
- **JATS XML Conversion**: Map JSON structure into JATS XML with 1:1 mapping guarantee
- **Rich Text Editing**: Use Lexical editor with bidirectional data binding to Redux state
- **Real-time Preview**: XML, HTML, and PDF preview modes with different templates
- **Modern UI**: Split-view layout with TailwindCSS, responsive design

## Tech Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for build tooling
- **Redux Toolkit** for state management
- **Lexical** for rich text editing
- **TailwindCSS** for styling
- **Axios** for API communication

### Backend
- **FastAPI** (Python)
- **OpenAI API** for AI-powered document parsing
- **python-docx** for DOCX file processing
- **Pydantic** for data validation

## Project Structure

```
ame-star/
├── src/                          # Frontend source code
│   ├── components/              # React components
│   │   ├── editor/             # Lexical editor components
│   │   ├── layout/             # Layout components
│   │   ├── preview/            # Preview system
│   │   └── upload/             # File upload
│   ├── hooks/                  # Custom React hooks
│   ├── store/                  # Redux store and slices
│   ├── types/                  # TypeScript type definitions
│   └── utils/                  # Utility functions
├── backend/                     # Backend source code
│   ├── models/                 # Pydantic models
│   ├── services/               # Business logic services
│   └── main.py                 # FastAPI application
├── demo.XML                    # JATS XML reference file
└── package.json               # Frontend dependencies
```

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Python 3.8+
- OpenAI API key

### Frontend Setup

1. Install dependencies:
```bash
npm install
```

2. Create environment file:
```bash
cp .env.example .env
```

3. Start the development server:
```bash
npm run dev
```

The frontend will be available at `http://localhost:3000`

### Backend Setup

1. Navigate to the backend directory:
```bash
cd backend
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Create environment file:
```bash
cp .env.example .env
```

5. Add your OpenAI API key to `.env`:
```
OPENAI_API_KEY=your_openai_api_key_here
```

6. Start the backend server:
```bash
python main.py
```

The backend will be available at `http://localhost:8000`

## Usage

1. **Upload Document**: Drag and drop a .docx file or click to browse
2. **AI Processing**: The system will parse the document using AI and convert it to JATS structure
3. **Edit Content**: Use the rich text editor to modify any field with real-time updates
4. **Preview**: Switch between XML, HTML, and PDF preview modes
5. **Export**: Download the final JATS XML or other formats

## Key Features

### State Management
- Redux store mirrors JATS XML structure exactly
- Bidirectional data binding between Lexical editors and Redux state
- Real-time synchronization ensures consistency

### Editor System
- Each JATS field has its own Lexical editor instance
- Rich text support: bold, italic, underline, lists, links
- Custom plugins for JATS-specific features

### Preview System
- **XML Preview**: Syntax-highlighted JATS XML output
- **HTML Preview**: Formatted article preview with academic styling
- **PDF Preview**: Print-ready format (coming soon)

### AI Integration
- OpenAI GPT-4 for document structure enhancement
- Automatic section detection and organization
- Content gap filling and formatting improvements

## API Endpoints

- `POST /api/upload-docx` - Upload and process DOCX file
- `POST /api/convert-to-xml` - Convert JATS document to XML
- `POST /api/convert-to-html` - Convert JATS document to HTML
- `POST /api/convert-to-pdf` - Convert JATS document to PDF
- `POST /api/ai-enhance` - Enhance content using AI

## Development

### Adding New Editor Fields

1. Define the field in JATS types (`src/types/jats.ts`)
2. Add editor state mapping in Redux slice
3. Create editor field component
4. Add to the main editor panel

### Customizing Preview Templates

1. Modify the conversion logic in `backend/services/jats_converter.py`
2. Update HTML/CSS templates for different article types
3. Add new preview modes as needed

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For questions or support, please open an issue on GitHub.
