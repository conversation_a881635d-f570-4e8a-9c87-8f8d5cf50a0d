// Simple integration test to verify the system works
// Run with: node test-integration.js

const fs = require('fs');
const path = require('path');

console.log('🧪 AME Star Integration Test');
console.log('============================');

// Check if all required files exist
const requiredFiles = [
  'package.json',
  'vite.config.ts',
  'tsconfig.json',
  'tailwind.config.js',
  'src/main.tsx',
  'src/App.tsx',
  'src/index.css',
  'src/store/index.ts',
  'src/store/documentSlice.ts',
  'src/types/jats.ts',
  'src/types/editor.ts',
  'src/hooks/useDocument.ts',
  'src/components/editor/LexicalEditor.tsx',
  'src/components/editor/EditorPanel.tsx',
  'src/components/preview/PreviewPanel.tsx',
  'src/components/layout/SplitView.tsx',
  'src/components/upload/DocumentUpload.tsx',
  'src/utils/api.ts',
  'src/utils/demoLoader.ts',
  'backend/main.py',
  'backend/requirements.txt',
  'backend/models/document.py',
  'backend/services/docx_parser.py',
  'backend/services/ai_service.py',
  'backend/services/jats_converter.py',
  'demo.XML',
  'README.md'
];

let missingFiles = [];
let existingFiles = [];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    existingFiles.push(file);
  } else {
    missingFiles.push(file);
  }
});

console.log(`✅ Found ${existingFiles.length} required files`);

if (missingFiles.length > 0) {
  console.log(`❌ Missing ${missingFiles.length} files:`);
  missingFiles.forEach(file => console.log(`   - ${file}`));
} else {
  console.log('✅ All required files are present');
}

// Check package.json dependencies
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = [
    '@lexical/react',
    '@reduxjs/toolkit',
    'react',
    'react-dom',
    'react-redux',
    'lexical',
    'axios',
    'react-dropzone'
  ];
  
  const missingDeps = requiredDeps.filter(dep => !packageJson.dependencies[dep]);
  
  if (missingDeps.length === 0) {
    console.log('✅ All required frontend dependencies are listed');
  } else {
    console.log(`❌ Missing frontend dependencies: ${missingDeps.join(', ')}`);
  }
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
}

// Check backend requirements
try {
  const requirements = fs.readFileSync('backend/requirements.txt', 'utf8');
  const requiredBackendDeps = [
    'fastapi',
    'uvicorn',
    'python-docx',
    'openai',
    'pydantic'
  ];
  
  const missingBackendDeps = requiredBackendDeps.filter(dep => 
    !requirements.includes(dep)
  );
  
  if (missingBackendDeps.length === 0) {
    console.log('✅ All required backend dependencies are listed');
  } else {
    console.log(`❌ Missing backend dependencies: ${missingBackendDeps.join(', ')}`);
  }
} catch (error) {
  console.log('❌ Error reading backend/requirements.txt:', error.message);
}

// Check demo.XML file
try {
  const demoXml = fs.readFileSync('demo.XML', 'utf8');
  if (demoXml.includes('<article') && demoXml.includes('</article>')) {
    console.log('✅ Demo JATS XML file is valid');
  } else {
    console.log('❌ Demo JATS XML file appears to be invalid');
  }
} catch (error) {
  console.log('❌ Error reading demo.XML:', error.message);
}

console.log('\n🎯 Integration Test Summary:');
console.log(`   Files: ${existingFiles.length}/${requiredFiles.length} present`);
console.log(`   Status: ${missingFiles.length === 0 ? '✅ PASS' : '❌ FAIL'}`);

if (missingFiles.length === 0) {
  console.log('\n🚀 System appears to be ready for development!');
  console.log('   Run ./start.sh to set up the development environment');
} else {
  console.log('\n⚠️  Please ensure all files are present before starting development');
}

console.log('\n📋 Next Steps:');
console.log('   1. Run: ./start.sh');
console.log('   2. Add OpenAI API key to backend/.env');
console.log('   3. Start backend: cd backend && source venv/bin/activate && python main.py');
console.log('   4. Start frontend: npm run dev');
console.log('   5. Open http://localhost:3000 in your browser');
