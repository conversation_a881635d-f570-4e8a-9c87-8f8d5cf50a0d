#!/bin/bash

# AME Star Development Startup Script

echo "🚀 Starting AME Star Development Environment"
echo "============================================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

# Frontend setup
echo "📦 Installing frontend dependencies..."
npm install

if [ ! -f ".env" ]; then
    echo "📝 Creating frontend environment file..."
    cp .env.example .env
fi

# Backend setup
echo "🐍 Setting up backend..."
cd backend

if [ ! -d "venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv venv
fi

echo "📦 Activating virtual environment and installing dependencies..."
source venv/bin/activate
pip install -r requirements.txt

if [ ! -f ".env" ]; then
    echo "📝 Creating backend environment file..."
    cp .env.example .env
    echo ""
    echo "⚠️  IMPORTANT: Please add your OpenAI API key to backend/.env"
    echo "   Edit backend/.env and set: OPENAI_API_KEY=your_key_here"
    echo ""
fi

cd ..

echo "✅ Setup complete!"
echo ""
echo "🚀 To start the development servers:"
echo "   1. Backend:  cd backend && source venv/bin/activate && python main.py"
echo "   2. Frontend: npm run dev"
echo ""
echo "📖 The application will be available at:"
echo "   - Frontend: http://localhost:3000"
echo "   - Backend:  http://localhost:8000"
echo "   - API Docs: http://localhost:8000/docs"
echo ""
echo "💡 Don't forget to set your OpenAI API key in backend/.env!"
