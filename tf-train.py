import json
import numpy as np
import tensorflow as tf
import tensorflow_hub as hub
import os

# --- 步骤 1: 从本地读取数据 ---
with open("data.json", "r", encoding="utf-8") as f:
    medical_data = json.load(f)

# 确定标签并创建映射
unique_labels = sorted(list(set(d['label'] for d in medical_data)))
label_to_index = {label: i for i, label in enumerate(unique_labels)}
num_classes = len(unique_labels)

print(f"发现 {num_classes} 个分类: {unique_labels}")

# 将标签保存到文件
if not os.path.exists('model'):
    os.makedirs('model')
with open('model/labels.json', 'w', encoding="utf-8") as f:
    json.dump(unique_labels, f, ensure_ascii=False, indent=2)

# 准备文本和标签
sentences = [d['text'] for d in medical_data]
labels = np.array([label_to_index[d['label']] for d in medical_data])
labels_one_hot = tf.keras.utils.to_categorical(labels, num_classes=num_classes)

# --- 步骤 2: 加载 USE 模型并生成嵌入向量 ---
print("正在加载 Universal Sentence Encoder (USE) 模型...")
use_model = hub.load("https://tfhub.dev/google/universal-sentence-encoder/4")

print("正在为文本生成嵌入向量...")
embeddings = use_model(sentences)

# --- 步骤 3: 构建并训练分类器 ---
embedding_size = embeddings.shape[1]

model = tf.keras.Sequential([
    tf.keras.layers.Input(shape=(embedding_size,), name="input_layer"),
    tf.keras.layers.Dense(128, activation='relu', name="hidden_layer_1"),
    tf.keras.layers.Dropout(0.5),
    tf.keras.layers.Dense(num_classes, activation='softmax', name="output_layer")
])

model.compile(optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
              loss='categorical_crossentropy',
              metrics=['accuracy'])

model.summary()

print("\n开始训练分类器...")
history = model.fit(
    embeddings,
    labels_one_hot,
    epochs=50,
    batch_size=16,
    shuffle=True,
    verbose=2
)

print("\n训练完成！")

# --- 步骤 4: 保存模型 ---
model.save('medical_classifier.h5')
print("\n模型已保存为 medical_classifier.h5")
print("接下来请运行：")
print("tensorflowjs_converter --input_format keras medical_classifier.h5 model/")