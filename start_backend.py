#!/usr/bin/env python3
"""
Simple backend startup script
"""

import os
import sys
import subprocess

def main():
    print("🚀 Starting AME Star Backend Server")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists('backend/main.py'):
        print("❌ Error: backend/main.py not found")
        print("Please run this script from the project root directory")
        sys.exit(1)
    
    # Check if requirements are installed
    try:
        import fastapi
        import uvicorn
        print("✅ FastAPI and Uvicorn are installed")
    except ImportError:
        print("❌ Missing dependencies. Please install:")
        print("   cd backend && pip install -r requirements.txt")
        sys.exit(1)
    
    # Check environment file
    if not os.path.exists('backend/.env'):
        print("⚠️  Warning: backend/.env not found")
        print("   Copy backend/.env.example to backend/.env")
        print("   Add your OpenAI API key to the .env file")
        print("")
    
    # Change to backend directory and start server
    os.chdir('backend')
    
    print("🚀 Starting server at http://localhost:8000")
    print("📖 API documentation at http://localhost:8000/docs")
    print("Press Ctrl+C to stop the server")
    print("")
    
    try:
        # Start the server
        subprocess.run([sys.executable, 'main.py'], check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
